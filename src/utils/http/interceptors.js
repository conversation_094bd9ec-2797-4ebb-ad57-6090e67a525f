import { getToken } from '@/utils'
import { resolveResError } from './helpers'
import { useUserStore } from '@/store'

export function reqResolve(config) {
  // 处理不需要token的请求
  if (config.noNeedToken) {
    return config
  }

  const token = getToken()
  if (token) {
    config.headers.token = config.headers.token || token
  }

  return config
}

export function reqReject(error) {
  return Promise.reject(error)
}

export function resResolve(response) {
  const isFileDownload =
    response.config?.responseType === 'blob' ||
    (response.headers &&
      response.headers['content-disposition'] &&
      response.headers['content-disposition'].includes('attachment')) ||
    (response.headers &&
      response.headers['content-type'] &&
      (response.headers['content-type'].startsWith(
        'application/vnd.openxmlformats-officedocument'
      ) ||
        response.headers['content-type'] === 'application/octet-stream' || // 通用二进制流
        response.headers['content-type'].startsWith('application/pdf') || // PDF示例
        response.headers['content-type'].startsWith('image/') || // 图片示例
        response.headers['content-type'].startsWith('text/csv'))) // CSV示例
  if (isFileDownload) {
    // 如果是文件下载，直接 resolve 整个 response 对象
    // 这样在调用处可以访问 response.data (Blob) 和 response.headers
    return Promise.resolve(response)
  }

  const { data, status, statusText } = response
  if (data?.code !== 200) {
    const code = data?.code ?? status
    /** 根据code处理对应的操作，并返回处理后的message */
    const message = resolveResError(code, data?.msg ?? statusText)
    window.$message?.error(message, { keepAliveOnHover: true })
    return Promise.reject({ code, message, error: data || response })
  }
  return Promise.resolve(data)
}

export async function resReject(error) {
  if (!error || !error.response) {
    const code = error?.code
    /** 根据code处理对应的操作，并返回处理后的message */
    const message = resolveResError(code, error.message)
    window.$message?.error(message)
    return Promise.reject({ code, message, error })
  }
  const { data, status } = error.response

  if (data?.code === 401) {
    try {
      const userStore = useUserStore()
      userStore.logout()
    } catch (error) {
      console.log('resReject error', error)
      return
    }
  }
  // 后端返回的response数据
  const code = data?.code ?? status
  const message = resolveResError(code, data?.msg ?? error.message)
  window.$message?.error(message, { keepAliveOnHover: true })
  return Promise.reject({ code, message, error: error.response?.data || error.response })
}
