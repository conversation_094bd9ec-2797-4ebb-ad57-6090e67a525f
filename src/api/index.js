import { request } from '@/utils'

export default {
  login: (data) => request.post('/v1/base/access_token', data, { noNeedToken: true }),
  getUserInfo: () => request.get('/v1/base/userinfo'),
  getUserMenu: () => request.get('/v1/base/usermenu'),
  getUserApi: () => request.get('/v1/base/userapi'),

  // profile
  updatePassword: (data = {}) => request.post('/v1/base/update_password', data),

  // users
  getUserList: (params = {}) => request.get('/v1/user/list', { params }),
  getUserById: (params = {}) => request.get('/v1/user/get', { params }),
  createUser: (data = {}) => request.post('/v1/user/create', data),
  updateUser: (data = {}) => request.post('/v1/user/update', data),
  deleteUser: (params = {}) => request.delete(`/v1/user/delete`, { params }),
  resetPassword: (data = {}) => request.post(`/v1/user/reset_password`, data),

  // role
  getRoleList: (params = {}) => request.get('/v1/role/list', { params }),
  createRole: (data = {}) => request.post('/v1/role/create', data),
  updateRole: (data = {}) => request.post('/v1/role/update', data),
  deleteRole: (params = {}) => request.delete('/v1/role/delete', { params }),
  updateRoleAuthorized: (data = {}) => request.post('/v1/role/authorized', data),
  getRoleAuthorized: (params = {}) => request.get('/v1/role/authorized', { params }),

  // menus
  getMenus: (params = {}) => request.get('/v1/menu/list', { params }),
  createMenu: (data = {}) => request.post('/v1/menu/create', data),
  updateMenu: (data = {}) => request.post('/v1/menu/update', data),
  deleteMenu: (params = {}) => request.delete('/v1/menu/delete', { params }),

  // apis
  getApis: (params = {}) => request.get('/v1/api/list', { params }),
  createApi: (data = {}) => request.post('/v1/api/create', data),
  updateApi: (data = {}) => request.post('/v1/api/update', data),
  deleteApi: (params = {}) => request.delete('/v1/api/delete', { params }),
  refreshApi: (data = {}) => request.post('/v1/api/refresh', data),

  // depts
  getDepts: (params = {}) => request.get('/v1/dept/list', { params }),
  createDept: (data = {}) => request.post('/v1/dept/create', data),
  updateDept: (data = {}) => request.post('/v1/dept/update', data),
  deleteDept: (params = {}) => request.delete('/v1/dept/delete', { params }),

  // auditlog
  getAuditLogList: (params = {}) => request.get('/v1/auditlog/list', { params }),

  //car_manage
  getCarsList: (params = {}) => request.get('/manage/cars/list', { params }),
  getOptionList: (params = {}) => request.get('/manage/cars/option_list', { params }), // 获取查询下拉框内容
  getOeDataList: (params = {}) => request.get('/manage/cars/oe_list', { params }), // 通过车型id获取oe信息
  getProductDetail: (params = {}) => request.get('/manage/cars/product_detail_info', { params }),
  getCarInfoByReachCarIds: (data = {}) =>
    request.post('/manage/cars/get_carInfo_by_reachCarIds', { data }),
  //oe_manage
  searchOe: (params = {}) => request.get('/manage/oes/parts_search', { params }),
  searchCarsByOE: (params = {}) => request.get('/manage/oes/serach_cars_by_oe', { params }),
  searchOEForComponent: (params = {}) => request.get('/manage/oes/oe_info_component', { params }),
  searchOEForComponentMainNum: (params = {}) =>
    request.get('/manage/oes/oe_info_component_mainNum', { params }),
  getReachCarIdsByReachNumber: (data = {}) =>
    request.post('/manage/oes/get_reachCarId_by_ReachNumber', { data }),
  getReachCarIdsByOes: (data = {}) =>
    request.post('/manage/oes/get_reachCarId_by_Oes', { data }),
  searchCompetitorForComponent: (data = {}) =>
    request.post('/manage/oes/competitor_info_component', { data }),

  //product_manage
  createProduct: (data = {}) => request.post('/manage/products/create_product_manual', data),
  searchProduct: (params = {}) => request.get('/manage/products/products_search', { params }),
  searchCarsByProduct: (params = {}) =>
    request.get('/manage/products/serach_cars_by_product', { params }),
  checkProductExist: (data = {}) => request.post('/manage/products/check_product_exist', data),
  productOptionList: (params = {}) => request.get('/manage/products/option_list', { params }),
  productDataList: (params = {}) => request.get('/manage/products/product_list', { params }),
  AddCommodityAttr: (data = {}) => request.post('/manage/products/add_commodity_attrs', data),
  minIOUploadAttachments: (data = {}) =>
    request.post('/manage/products/minio/products/upload', data),
  minIODeleteAttachments: (data = {}) =>
    request.post('/manage/products/minio/products/removeOneObject', data),
  saveProductAttachments: (data = {}) =>
    request.post('/manage/products/minio/products/saveProductAttachments', data),
  verifyReplacementInfo: (data = {}) => request.post('/manage/products/verify_replacement_info', data),
  downloadTemplateFile: (params = {}) =>
    request.get('/manage/products/download_template', { params }),
  uploadFileForPreview: (data = {}) =>
    request.post('/manage/products/upload_file_for_preview', data),
  generateFinalData: (data = {}) => request.post('/manage/products/generate_final_data', data),
  importToDatabase: (data = {}) => request.post('/manage/products/import_data_for_review', data),
  commodityOptionList: (params = {}) =>
    request.get('/manage/products/commodity_options', { params }),
  commodityDataList: (params = {}) =>
    request.get('/manage/products/commodity_detail_list', { params }),
  getPendingApprovals: (params = {}) =>
    request.get('/manage/products/get_pending_approvals', { params }),
  approveItems: (data = {}) => request.post('/manage/products/approve_items', data),
  getReferenceInfo: (params = {}) => request.get('/manage/products/get_reference_info', { params }),
  getReplacementInfo: (params = {}) =>
    request.get('/manage/products/get_replacements_info', { params }),
  getPriceCategoryInfo: (params = {}) =>
    request.get('/manage/products/get_price_category_info', { params }),
  getSupplyList: (params = {}) => request.get('/manage/products/get_supply_list', { params }),
  getProductInfoByCode: (params = {}) =>
    request.get('/manage/products/search_product_info', { params }),

  // category
  updateTableStatus: (data = {}) => request.post('/manage/dicts/update_table_status', data),
  updateTagsFieldsStatus: (data = {}) =>
    request.post('/manage/dicts/update_fields_tags_status', data),
  getCategoryTableList: (params = {}) =>
    request.get('/manage/dicts/list_table_category', { params }),
  getCategoryDetailTable: (params = {}) =>
    request.get('/manage/dicts/category_detail_table_list', { params }),
  getTagTreeData: (params = {}) => request.get('/manage/dicts/tags_tree_data', { params }),
  getDynamicColumns: (params = {}) => request.get('/manage/dicts/get_dynamic_columns', { params }),
  getDynamicTableData: (params = {}) =>
    request.get('/manage/dicts/get_dynamic_table_data', { params }),
  getCategoryList: (params = {}) => request.get('/manage/dicts/list_category', { params }),
  getQueryResultList: (data = {}) => request.post('/manage/dicts/list_query_result', data),
  getTableList: (params = {}) => request.get('/manage/dicts/list_table', { params }),
  getTableFieldsList: (params = {}) => request.get('/manage/dicts/list_table_fields', { params }),
  updateSourceData: (params = {}) => request.post('/manage/dicts/update_data', { params }),
  deleteCategory: (data = {}) => request.post('/manage/dicts/delete_category', data),
  createCategory: (data = {}) => request.post('/manage/dicts/create_category', data),
  TableCategoryHandler: (data = {}) => request.post('/manage/dicts/table_category_handler', data),
  ProductCategorySuffix: (params = {}) =>
    request.get('/manage/dicts/get_category_suffix', { params }),

  //data_source
  getTableInfoList: (params = {}) => request.get('/manage/datasource/get_table_list', { params }),
  getTableSampleData: (params = {}) =>
    request.get('/manage/datasource/get_table_detail_info', { params }),
  uploadTableData: (data = {}) => request.post('/manage/datasource/upload_source_file', data),
  getUploadHistory: (params = {}) => request.get('/manage/datasource/upload_history', { params }),

  //Search
  searchVin: (params = {}) => request.get('/manage/search/vin_search', { params }),
  getOEReachByVin: (params = {}) => request.get('/manage/search/oe_reach_by_vin', { params }),
  searchOE: (params = {}) => request.get('/manage/search/oe_search', { params }),
  reachPreSearch: (params = {}) => request.get('/manage/search/reach_pre_search', { params }),
  searchReach: (params = {}) => request.get('/manage/search/reach_search', { params }),
  searchReference: (params = {}) => request.get('/manage/search/reference_search', { params }),
  getSourceInfo: (params = {}) => request.post('/manage/dicts/get_source_info', { params }),

  // Task Management - 任务管理
  getTaskList: (params = {}) => request.get('/manage/tasks/list', { params }),
  getTaskDetail: (params = {}) => request.get('/manage/tasks/detail', { params }),
  createTask: (data = {}) => request.post('/manage/tasks/create', data),
  updateTask: (data = {}) => request.post('/manage/tasks/update', data),
  deleteTask: (params = {}) => request.delete('/manage/tasks/delete', { params }),
  completeTask: (data = {}) => request.post('/manage/tasks/complete', data),
  assignTask: (data = {}) => request.post('/manage/tasks/assign', data),
  getTasksByUser: (params = {}) => request.get('/manage/tasks/user_tasks', { params }),
  getTaskStatistics: (params = {}) => request.get('/manage/tasks/statistics', { params }),

  // supplier
  getSupplierMap: (params = {}) => request.get('/manage/suppliers/get_all_suppliers', { params }),
  // Vehicle and OE Management
  createVehicle: (data = {}) => request.post('/manage/vehicles/create', data),
  createOE: (data = {}) => request.post('/manage/oes/create', data),
  getProductLine: (params = {}) => request.post('/manage/dicts/get_product_line', { params }),
  getBrand: (params = {}) => request.post('/manage/dicts/get_brand', { params }),
  getInquiry: (params = {}) => request.post('/manage/dicts/get_inquiry', { params }),
  getMarket: (params = {}) => request.post('/manage/dicts/get_market', { params }),
  get_batchinquiry: (params = {}) => request.post('/manage/dicts/get_batchinquiry', { params }),
}
