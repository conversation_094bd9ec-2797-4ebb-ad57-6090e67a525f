<template>
    <div>
      <n-button type="primary" @click="openImportModal">批量导入</n-button>
  
      <n-modal
        v-model:show="showBatchImportModal"
        preset="card"
        :style="{ width: '900px' }"
        :title="modalTitle"
        :mask-closable="false"
        :closable="true"
        @after-leave="resetAllState"
      >
        <n-spin :show="BatchImportLoading">
          <n-steps :current="BatchImportCurrentStep" :status="BatchImportStepStatus" style="margin-bottom: 20px;">
            <n-step title="上传文件" description="上传数据文件并填写备注" />
            <n-step title="预览确认" description="预览处理结果并确认生成" />
            <n-step title="导入结果" description="查看最终导入结果" />
          </n-steps>
  
          <div v-if="BatchImportCurrentStep === 1">
            <n-form-item label="备注">
              <n-input
                v-model:value="remarks"
                type="textarea"
                placeholder="请输入备注信息 (可选)"
                :autosize="{ minRows: 3 }"
              />
            </n-form-item>
            <n-form-item label="模板下载">
              <n-button @click="downloadTemplate">下载模板</n-button>
            </n-form-item>
            <n-form-item label="选择文件">
              <n-upload
                action="#"
                :default-upload="false"
                v-model:file-list="fileList"
                :max="1"
                @change="handleFileChange"
                accept=".csv,.xlsx,.xls"
              >
                <n-button>选择文件</n-button>
              </n-upload>
            </n-form-item>
            <n-space justify="end" style="margin-top: 20px;">
              <n-button @click="uploadAndPreview" type="primary" :disabled="!selectedFile">上传并预览</n-button>
              <n-button @click="closeModal">取消</n-button>
            </n-space>
          </div>
  
          <div v-if="BatchImportCurrentStep === 2">
            <n-h4>数据预览与处理结果</n-h4>
            <n-data-table
              :columns="previewTable.columns"
              :data="previewTable.data"
              :pagination="false"
              :max-height="300"
              :bordered="true"
              :scroll-x="1200"
              :single-line="false"
              style="width: 100%;"
            />
            <n-space justify="end" style="margin-top: 20px;">
              <n-button @click="exportResults(previewTable.data, 'preview_data')">导出预览结果</n-button>
              <n-button type="primary" @click="confirmGeneration">确定生成</n-button>
              <n-button @click="BatchImportCurrentStep = 1">上一步</n-button>
              <n-button @click="closeModal">取消</n-button>
            </n-space>
          </div>
  
          <div v-if="BatchImportCurrentStep === 3">
            <n-h4>最终生成结果</n-h4>
             <n-data-table
              :columns="finalTable.columns"
              :data="finalTable.data"
              :pagination="false"
              :max-height="300"
              :bordered="true"
            />
            <n-space justify="end" style="margin-top: 20px;">
              <n-button @click="exportResults(finalTable.data, 'final_generated_results')">导出生成结果</n-button>
              <n-button type="primary" @click="completeImportToDatabase" :loading="importToDbLoading">完成入库</n-button>
              <n-button @click="BatchImportCurrentStep = 2">上一步</n-button>
              <n-button @click="closeModal">取消</n-button>
            </n-space>
          </div>
        </n-spin>
      </n-modal>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed } from 'vue';
  import {
    NModal, NButton, NSelect, NInput, NUpload, NDataTable, NSpin,
    NSteps, NStep, NSpace, NFormItem, NH4, useMessage,
    type UploadFileInfo, type DataTableColumns
  } from 'naive-ui';
  import { saveAs } from 'file-saver'; // For CSV export
  import api from '@/api'
  import * as XLSX from 'xlsx';
  
  const message = useMessage();
  
  // Modal State
  const showBatchImportModal = ref(false);
  const BatchImportCurrentStep = ref(1);
  const BatchImportLoading = ref(false);
  const importToDbLoading = ref(false); // 单独的入库loading状态
  const BatchImportStepStatus = computed(() => (BatchImportLoading.value ? 'process' : 'finish'));
  
  const modalTitle = computed(() => {
    switch (BatchImportCurrentStep.value) {
      case 1: return '步骤 1: 上传文件与备注';
      case 2: return '步骤 2: 预览并确认';
      case 3: return '步骤 3: 完成入库';
      default: return '批量导入';
    }
  });
  
  // --- Step 1 State ---
  const remarks = ref('');
  const fileList = ref<UploadFileInfo[]>([]);
  const selectedFile = computed(() => fileList.value[0]?.file || null);
  
  // --- Step 2 State (Preview Table) ---
  const previewTable = ref<{ columns: DataTableColumns<any>, data: any[] }>({
    columns: [],
    data: [],
  });
  
  // --- Step 3 State (Final Table) ---
  const finalTable = ref<{ columns: DataTableColumns<any>, data: any[] }>({
    columns: [],
    data: [],
  });
  
  // --- Functions ---
  
  const openImportModal = () => {
    resetAllState(); // Ensure clean state when opening
    showBatchImportModal.value = true;
    BatchImportCurrentStep.value = 1;
  };
  
  const closeModal = () => {
    showBatchImportModal.value = false;
    // resetAllState will be called by @after-leave
  };
  
  const resetAllState = () => {
    BatchImportCurrentStep.value = 1;
    remarks.value = '';
    fileList.value = [];
    previewTable.value = { columns: [], data: [] };
    finalTable.value = { columns: [], data: [] };
    BatchImportLoading.value = false;
    importToDbLoading.value = false;
  };
  
  // Step 1 Actions
  const downloadTemplate = async () => {
    BatchImportLoading.value = true;
    message.info(`正在请求模板下载...`);
  
    try {
        const response = await api.downloadTemplateFile();

        let filenameFromServer = '物料申请表_模板.xlsx'; // 默认值
        const contentDisposition = response.headers['content-disposition'];
        if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?(.+)"?/i);
        if (filenameMatch && filenameMatch.length > 1) {
            filenameFromServer = filenameMatch[1];
        }
        }
        console.log(contentDisposition)
        const blob = new Blob([response.data], { type: response.headers['content-type'] || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        console.log(blob)
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = filenameFromServer;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(link.href);
    
    message.success(`模板下载成功!`);
  } catch (error) {
    console.error('下载失败:', error);
    message.error(error.message || '下载失败，请重试');
  } finally {
    BatchImportLoading.value = false;
  }
  };
  
  // Step 1 Actions - 上传文件并获取预览数据
  const handleFileChange = (options: { file: UploadFileInfo, fileList: UploadFileInfo[] }) => {
    // fileList is automatically updated by v-model
    if (options.fileList.length > 0) {
       message.success(`已选择文件: ${options.fileList[0].name}`);
    } else {
       message.info('已清除选择的文件');
    }
  };

  // 辅助函数：计算列宽
const calculateColumnWidth = (title) => {
  if (!title) return 120;
  
  // 计算中文字符数量
  const chineseCharCount = (title.match(/[\u4e00-\u9fa5]/g) || []).length;
  const englishCharCount = title.length - chineseCharCount;
  
  // 中文字符约16px，英文字符约9px
  const estimatedWidth = chineseCharCount * 16 + englishCharCount * 9;
  
  // 最小80px，最大250px，加上padding
  return Math.max(80, Math.min(250, estimatedWidth + 30));
};

// 处理列配置，添加动态宽度
const processColumnsWithDynamicWidth = (columns) => {
  return columns.map(col => ({
    ...col,
    width: calculateColumnWidth(col.title || col.key),
    ellipsis: {
      tooltip: true
    },
    resizable: true,
  }));
};

  const uploadAndPreview = async () => {
    if (!selectedFile.value) {
      message.warning('请选择要导入的文件');
      return;
    }
    BatchImportLoading.value = true;
    // message.loading('正在解析文件并生成预览数据，请稍候...');
  
    try {
      const formData = new FormData()
      formData.append('file', selectedFile.value)
      formData.append('remarks', remarks.value)

      const response = await api.uploadFileForPreview(formData);
    
      const processedColumns = processColumnsWithDynamicWidth(response.table_columns)

      previewTable.value.columns = processedColumns
      previewTable.value.data = response.data
  
      BatchImportCurrentStep.value = 2;
      message.success('文件解析完成，请确认预览数据!');
    } catch (error) {
      console.error('文件上传失败:', error);
      message.error(error.message || '文件处理失败，请重试');
    } finally {
      BatchImportLoading.value = false;
    }
  };
  
  // Step 2 Actions - 确定生成最终数据
  const confirmGeneration = async () => {
    BatchImportLoading.value = true;
    // message.loading('正在生成最终数据，请稍候...');
  
    try {
    const processedData = previewTable.value.data.map((item, index) => {
      const processedItem = {
        ...item
      };
      return processedItem;})
    
      const response = await api.generateFinalData(processedData);
  
      // 设置最终表格数据 (实际应该来自后端响应)
      finalTable.value.columns = previewTable.value.columns.filter(column => 
                !['part_verify_desc', 'part_status'].includes(column.key));

      finalTable.value.data = response.data

      BatchImportCurrentStep.value = 3;
      message.success('最终数据生成完成，请确认后点击完成入库!');
    } catch (error) {
      console.error('数据生成失败:', error);
      message.error(error.message || '数据生成失败，请重试');
    } finally {
      BatchImportLoading.value = false;
    }
  };
  
  // Step 3 Actions - 真正的入库操作
  const completeImportToDatabase = async () => {
    importToDbLoading.value = true;
    message.loading('正在执行入库操作，请稍候...');
  
    try {
      const processedData = finalTable.value.data.map((item, index) => {
        const processedItem = {
            ...item
        };
        return processedItem;})

      const response = await api.importToDatabase({
        finalData: processedData,
        remarks: remarks.value,
      });
  
      message.success('批量导入入库完成！数据已成功保存, 等待审核。');
      
      closeModal();
    } catch (error) {
      console.error('入库操作失败:', error);
      message.error(error.message || '入库操作失败，请重试');
    } finally {
      importToDbLoading.value = false;
    }
  };
  
  // Generic Export Function
  const exportResults = (data: any[], filenamePrefix: string) => {
  if (!data || data.length === 0) {
    message.warning('没有数据可以导出');
    return;
  }

  BatchImportLoading.value = true;
  message.info('正在准备导出数据...');

  // Simulate some processing if needed
  setTimeout(() => {
    try {
      const columns = BatchImportCurrentStep.value === 2 ? previewTable.value.columns : finalTable.value.columns;
      
      // 准备Excel数据
      const excelData = data.map(row => {
        const rowData: any = {};
        columns.forEach(col => {
          const header = col.title || col.key;
          rowData[header] = row[col.key as string] || '';
        });
        return rowData;
      });

      // 创建工作簿和工作表
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

      // 设置列宽（可选）
      const colWidths = columns.map(col => ({
        wch: Math.max(10, (col.title || col.key).length + 2)
      }));
      worksheet['!cols'] = colWidths;

      // 生成Excel文件并下载
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${filenamePrefix}_${timestamp}.xlsx`;
      
      XLSX.writeFile(workbook, filename);
      message.success('数据导出成功!');
    } catch (error) {
      console.error('Export error:', error);
      message.error('导出失败，请查看控制台');
    } finally {
      BatchImportLoading.value = false;
    }
  }, 500);
};
  
  </script>
  
  <style scoped>
  /* Add any custom styles if needed */
  .n-form-item {
    margin-bottom: 18px;
  }
  </style>