<template>
  <CommonPage>
    <n-grid :cols="20" :x-gap="12" class="mb-4">
      <n-gi :span="5">
        <n-form-item label="物料代码" :label-width="80" label-placement="left">
          <n-input
            v-model:value="queryItems.MaterialCode"
            placeholder="物料代码"
            clearable
            @update:value="(v) => handleOptionChange('MaterialCode', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="5">
        <n-form-item label="客户代码" :label-width="80" label-placement="left">
          <n-input
            v-model:value="queryItems.CustomerCode"
            placeholder="客户代码"
            clearable
            @update:value="(v) => handleOptionChange('CustomerCode', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="5">
        <n-form-item label="包装代码" :label-width="80" label-placement="left">
          <n-input
            v-model:value="queryItems.PackagingCode"
            placeholder="包装代码"
            clearable
            @update:value="(v) => handleOptionChange('PackagingCode', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="5">
        <n-form-item label="零件名称" :label-width="80" label-placement="left">
          <n-select
            v-model:value="queryItems.ChineseName"
            :options="ChineseName_options"
            placeholder="零件名称"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('ChineseName', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="5">
        <n-form-item label="属性" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.Attributes"
            :options="Attributes_options"
            placeholder="属性"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('Attributes', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="5">
        <n-form-item label="可供状态" :label-width="80" label-placement="left">
          <n-select
            v-model:value="queryItems.Availability"
            :options="Availability_options"
            placeholder="可供状态"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('Availability', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-button type="primary" @click="handleSearch">搜索</n-button>
        <n-button class="ml-2" @click="customReset">重置</n-button>
      </n-gi>
    </n-grid>
    <div class="dynamic-table-container">
      <div class="table-toolbar">
        <n-space>
          <n-popover
            ref="columnSelectorPopoverRef"
            trigger="click"
            placement="bottom-end"
            style="padding: 0"
            :on-update:show="handlePopoverUpdateShow"
          >
            <template #trigger>
              <n-button :icon="SettingsIcon">选择列</n-button>
            </template>
            <div class="column-selector-panel">
              <div
                style="
                  padding: 12px 12px 0 12px;
                  font-weight: bold;
                  border-bottom: 1px solid #eee;
                  margin-bottom: 8px;
                "
              >
                自定义显示列
              </div>
              <n-scrollbar style="max-height: 300px; padding: 0 12px">
                <n-checkbox-group v-model:value="tempVisibleColumnKeys">
                  <div v-for="column in allColumns" :key="column.key" class="column-selector-item">
                    <n-checkbox
                      :value="column.key"
                      :label="column.title"
                      :disabled="
                        tempVisibleColumnKeys.length === 1 &&
                        tempVisibleColumnKeys[0] === column.key
                      "
                    />
                  </div>
                </n-checkbox-group>
              </n-scrollbar>
              <div class="column-selector-actions">
                <n-button size="small" @click="resetToDefaultColumns">重置</n-button>
                <div>
                  <n-button size="small" style="margin-right: 8px" @click="cancelColumnSelection"
                    >取消</n-button
                  >
                  <n-button size="small" type="primary" @click="applyColumnSelection"
                    >确定</n-button
                  >
                </div>
              </div>
            </div>
          </n-popover>
        </n-space>
      </div>

      <n-data-table
        v-model:checked-row-keys="checkedRowKeys"
        :columns="displayColumns"
        :data="tableData"
        :bordered="true"
        :single-line="false"
        flex-height
        style="height: 480px"
        :pagination="pagination"
        :remote="true"
        :row-key="rowKey"
        @update:page="handleUpdatePage"
        @update:page-size="handleUpdatePageSize"
        @update:checked-row-keys="handleCheck"
      />
    </div>
  </CommonPage>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import {
  NDataTable,
  NButton,
  NCheckbox,
  NCheckboxGroup,
  NPopover,
  NScrollbar,
  useMessage,
} from 'naive-ui'
import api from '@/api'
import CommonPage from '@/components/page/CommonPage.vue'
import { SettingsSharp as SettingsIcon } from '@vicons/ionicons5'

defineOptions({ name: '商品数据查询' })

const queryItems = ref({
  CustomerCode: null,
  PackagingCode: null,
  MaterialCode: null,
  ChineseName: null,
  Attributes: null,
  Availability: null,
})

const ChineseName_options = ref([])
const Attributes_options = ref([])
const Availability_options = ref([])
const search_loading = ref(false)

const fetchInitOptions = async () => {
  search_loading.value = true
  try {
    const api_options = await api.commodityOptionList()
    ChineseName_options.value = api_options.data.ChineseName || []
    Attributes_options.value = api_options.data.Attributes || []
    Availability_options.value = api_options.data.Availability || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    search_loading.value = false // 完成加载
  }
}

const handleOptionChange = async (optionKey, selectedValue) => {
  search_loading.value = true
  try {
    queryItems.value[optionKey] = selectedValue
    // 从后端获取并更新其它选项
    const updatedOptions = await api.commodityOptionList(queryItems.value)
    ChineseName_options.value = updatedOptions.data.ChineseName || []
    Attributes_options.value = updatedOptions.data.Attributes || []
    Availability_options.value = updatedOptions.data.Availability || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    search_loading.value = false
  }
}

const rowKey = (row) => row.id
// 选中的行 keys
const checkedRowKeys = ref([])
const handleCheck = (keys) => {
  console.log('Checked keys changed:', keys)
  // checkedRowKeys.value = keys; // v-model 会自动更新
}

const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 10,
  pageSizes: [10, 20, 50, 100, 500],
  showSizePicker: true,
})

async function handleSearch() {
  pagination.page = 1
  await fetchData()
}

function handlePageChange(page) {
  pagination.page = page
  fetchData()
}

function handlePageSizeChange(pageSize) {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchData()
}

const handleUpdatePage = (page) => {
  handlePageChange(page)
}

const handleUpdatePageSize = (pageSize) => {
  handlePageSizeChange(pageSize)
}

async function fetchData() {
  try {
    const params = {
      ...queryItems.value,
      page: pagination.page,
      pageSize: pagination.pageSize,
    }
    const res = await api.commodityDataList(params)
    tableData.value = res.data
    pagination.itemCount = res.total
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

async function customReset() {
  // 清空 queryItems 的内容
  queryItems.value = {
    CustomerCode: null,
    PackagingCode: null,
    MaterialCode: null,
    ChineseName: null,
    Attributes: null,
    Availability: null,
  }
  // 重新填充所有下拉框的数据
  await fetchInitOptions()
  await handleSearch()
}

// 使用 Message API (确保你的 App.vue 或顶层组件有 <n-message-provider>)
const message = useMessage()

// localStorage 键名
const LOCAL_STORAGE_KEY = 'MY_DYNAMIC_TABLE_VISIBLE_COLUMNS'

// Popover 实例引用
const columnSelectorPopoverRef = ref(null)

// 1. 定义所有可能的列
const allColumns = [
  {
    title: '物料代码',
    key: 'MaterialCode',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '主号',
    key: 'MainNo',
    width: 120,
    ellipsis: { tooltip: true },
  },
  {
    title: '品牌',
    key: 'Brand',
    width: 80,
    ellipsis: { tooltip: true },
  },
  {
    title: '中文名称',
    key: 'ChineseName',
    width: 120,
    ellipsis: { tooltip: true },
  },
  {
    title: '规格型号',
    key: 'SpecModel',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '属性',
    key: 'Attributes',
    width: 80,
    ellipsis: { tooltip: true },
  },
  {
    title: '可供状态',
    key: 'Availability',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '客户代码',
    key: 'customer_code',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '包装代码',
    key: 'packaging_code',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '是否同步金蝶',
    key: 'kingdee_sync',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '适用市场',
    key: 'Market',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '车系英文',
    key: 'SeriesEN',
    width: 180,
    ellipsis: { tooltip: true },
  },
  {
    title: '车型英文',
    key: 'ModelEN',
    width: 180,
    ellipsis: { tooltip: true },
  },
  {
    title: '车系中文',
    key: 'SeriesCN',
    width: 180,
    ellipsis: { tooltip: true },
  },
  {
    title: '车型中文',
    key: 'ModelCN',
    width: 180,
    ellipsis: { tooltip: true },
  },
  {
    title: '内销OE',
    key: 'DomesticOE',
    width: 120,
    ellipsis: { tooltip: true },
  },
  {
    title: '内销标签车型',
    key: 'DomesticLabelModel',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '有无照片',
    key: 'HasPhoto',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '是否同步007',
    key: 'Sync007',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '007优良等级',
    key: 'QualityLevel007',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '参考号',
    key: 'ReferenceNo',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '是否做目录',
    key: 'IsCataloged',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '是否新品发布',
    key: 'IsNewRelease',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '新品发布日期',
    key: 'NewReleaseDate',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: 'EAN号',
    key: 'EAN',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: 'HS编码',
    key: 'HSCode',
    width: 140,
    ellipsis: { tooltip: true },
  },
  {
    title: '长',
    key: 'Length',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '宽',
    key: 'Width',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '高',
    key: 'Height',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '体积',
    key: 'Volume',
    width: 90,
    ellipsis: { tooltip: true },
  },
  {
    title: '替代说明',
    key: 'ReplacementDesc',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '供应商名称',
    key: 'SupplierName',
    width: 250,
    ellipsis: { tooltip: true },
  },
  {
    title: '默认供应商ID',
    key: 'DefaultSupplierID',
    width: 160,
    ellipsis: { tooltip: true },
  },
  {
    title: '备注',
    key: 'Remarks',
    width: 160,
    ellipsis: { tooltip: true },
  },
]

// 表格数据 (多造几条用于测试滚动和固定列)
const tableData = ref([])

// 2. 维护实际可见列的键 (从 localStorage 初始化或使用默认)
const visibleColumnKeys = ref([])
// 临时存储在 Popover 中选择的列，点击“确定”后才应用
const tempVisibleColumnKeys = ref([])

// 默认显示的列 (例如，选择所有列，或者一个预设的子集)
const getDefaultVisibleKeys = () => {
  //   allColumns.map((col) => col.key)
  return [
    'MaterialCode',
    'DomesticOE',
    'ChineseName',
    'SpecModel',
    'ReplacementDesc',
    'SupplierName',
    'Availability',
    'actions',
  ]
}
// 初始化 visibleColumnKeys
const initializeVisibleColumns = () => {
  const savedKeysJson = localStorage.getItem(LOCAL_STORAGE_KEY)
  let keysToUse = getDefaultVisibleKeys() // 默认值

  if (savedKeysJson) {
    try {
      const savedKeys = JSON.parse(savedKeysJson)
      if (Array.isArray(savedKeys) && savedKeys.every((key) => typeof key === 'string')) {
        // 过滤掉在 allColumns 中已不存在的 key
        const validSavedKeys = savedKeys.filter((key) => allColumns.some((col) => col.key === key))
        if (validSavedKeys.length > 0) {
          keysToUse = validSavedKeys
        }
      }
    } catch (error) {
      console.error('从localStorage加载列配置失败:', error)
      // 出错则使用默认值
    }
  }
  visibleColumnKeys.value = [...keysToUse]
  tempVisibleColumnKeys.value = [...keysToUse] // 初始化临时选择
}

onMounted(() => {
  fetchInitOptions()
  fetchData()
  initializeVisibleColumns()
})

// 3. 计算实际显示的列 (基于 visibleColumnKeys)
const displayColumns = computed(() => {
  return allColumns
    .filter((column) => visibleColumnKeys.value.includes(column.key))
    .sort((a, b) => {
      // 保持原始顺序
      const indexA = allColumns.findIndex((col) => col.key === a.key)
      const indexB = allColumns.findIndex((col) => col.key === b.key)
      return indexA - indexB
    })
})

// 4. Popover 相关逻辑
const handlePopoverUpdateShow = (show) => {
  if (show) {
    // Popover 打开时，用当前的实际显示列重置临时选择
    tempVisibleColumnKeys.value = [...visibleColumnKeys.value]
  }
}

// const handleTempSelectionChange = (selectedKeys) => {
// 这里不需要做什么特别的，因为 n-checkbox-group 的 :disabled 已经处理了至少一列
// 如果有其他复杂逻辑，可以在这里处理 tempVisibleColumnKeys
// }

const applyColumnSelection = () => {
  if (tempVisibleColumnKeys.value.length === 0) {
    message.error('至少需要选择一列！')
    return
  }
  visibleColumnKeys.value = [...tempVisibleColumnKeys.value]
  localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(visibleColumnKeys.value))
  message.success('列配置已更新')
  columnSelectorPopoverRef.value?.setShow(false) // 关闭 Popover
}

const cancelColumnSelection = () => {
  // 重置临时选择为当前实际的列，然后关闭
  tempVisibleColumnKeys.value = [...visibleColumnKeys.value]
  columnSelectorPopoverRef.value?.setShow(false) // 关闭 Popover
}

const resetToDefaultColumns = () => {
  tempVisibleColumnKeys.value = getDefaultVisibleKeys()
  // 如果希望重置后立即应用并保存，则调用 apply
  // applyColumnSelection();
  // 或者只是让用户在popover里看到重置效果，点确定再生效
  message.info('已重置为默认列，请点击确定应用。')
}
</script>

<style scoped>
.dynamic-table-container {
  padding: 15px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px); /* 示例：使其占据一定高度以测试表格 flex-height */
}

.table-toolbar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.column-selector-panel {
  /* min-width: 200px; 根据内容调整 */
  /* max-width: 300px; */
}

.column-selector-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  /* width: 100%; */
}

.column-selector-item .n-checkbox {
  width: 100%; /* 让 label 也可以点击触发 */
}

.column-selector-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-top: 1px solid #eee;
  margin-top: 8px;
}
</style>
