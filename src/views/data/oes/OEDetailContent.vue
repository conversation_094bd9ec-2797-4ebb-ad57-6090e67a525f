<template>
  <div class="oe-detail-content">
    <!-- 标签页导航 -->
    <n-tabs v-model:value="activeTab" type="line" size="large" class="detail-tabs">
      <n-tab-pane name="basic" tab="基本信息">
        <!-- 基本信息内容 -->
        <div class="basic-info-section">
          <n-grid :cols="24" :x-gap="20">
            <!-- 左侧信息 -->
            <n-gi :span="8">
              <div class="info-group">
                <div class="info-item">
                  <label class="info-label">OE号*</label>
                  <div class="info-value">{{ oeData.oe_number || '30008336' }}</div>
                  <div class="info-note">Item to be manufactured or repacked</div>
                </div>

                <div class="info-item">
                  <label class="info-label">OE名称</label>
                  <div class="info-value">{{ oeData.oe_name || '发动机散热器' }}</div>
                </div>

                <div class="info-item">
                  <label class="info-label">生产商</label>
                  <div class="info-value">{{ oeData.manufacturer || 'Denso' }}</div>
                </div>

                <div class="info-item">
                  <label class="info-label">补充配件</label>
                  <div class="info-value">
                    <n-tag type="info" size="small">密封圈 (4)</n-tag>
                  </div>
                </div>
              </div>
            </n-gi>

            <!-- 中间信息 -->
            <n-gi :span="8">
              <div class="info-group">
                <div class="info-item">
                  <label class="info-label">材料*</label>
                  <div class="info-value">{{ oeData.material || 'Aluminium' }}</div>
                  <div class="info-note">Item to be manufactured or repacked</div>
                </div>

                <div class="info-item">
                  <label class="info-label">生产编号</label>
                  <div class="info-value">{{ oeData.production_number || '4580-249' }}</div>
                </div>

                <div class="info-item">
                  <label class="info-label">用量</label>
                  <div class="info-value">{{ oeData.quantity || '1' }}</div>
                </div>

                <div class="info-item">
                  <label class="info-label">位置</label>
                  <div class="info-value">{{ oeData.position || '冷却散热风扇模块' }}</div>
                </div>
              </div>
            </n-gi>

            <!-- 右侧状态和项目信息 -->
            <n-gi :span="8">
              <div class="info-group">
                <div class="status-section">
                  <n-checkbox :checked="true" disabled>
                    <span class="status-text">适用现在使用</span>
                  </n-checkbox>
                  <n-checkbox :checked="false" disabled>
                    <span class="status-text">适用Reach产品</span>
                  </n-checkbox>
                </div>

                <div class="project-section">
                  <label class="info-label">Project</label>
                  <div class="project-value">PROJ-001</div>
                </div>

                <div class="notes-section">
                  <label class="info-label">备注*</label>
                  <div class="notes-content">
                    <p>纳新的散热器及第二代</p>
                    <p>压力盖 两层单热网，参杂约15</p>
                  </div>
                  <div class="notes-footer">
                    Quantity of item obtained after manufacturing / repacking from given quantities
                    of raw materials.
                  </div>
                </div>
              </div>
            </n-gi>
          </n-grid>
        </div>
      </n-tab-pane>

      <n-tab-pane name="vehicles" tab="适用车型">
        <!-- 适用车型表格 -->
        <div class="vehicles-section">
          <n-data-table
            :columns="vehicleColumns"
            :data="vehicleData"
            :pagination="vehiclePagination"
            :bordered="false"
            size="small"
          />
        </div>
      </n-tab-pane>

      <n-tab-pane name="replacement" tab="替代关系">
        <!-- 替代关系表格 -->
        <div class="replacement-section">
          <n-data-table
            :columns="replacementColumns"
            :data="replacementData"
            :bordered="false"
            size="small"
            class="replacement-table"
          />
        </div>
      </n-tab-pane>

      <n-tab-pane name="reach" tab="Reach适配">
        <!-- Reach适配表格 -->
        <div class="reach-section">
          <n-data-table :columns="reachColumns" :data="reachData" :bordered="false" size="small" />
        </div>
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { NTabs, NTabPane, NGrid, NGi, NDataTable, NTag, NCheckbox } from 'naive-ui'

// 接收父组件传递的数据
const props = defineProps({
  oeData: {
    type: Object,
    default: () => ({}),
  },
})

// 响应式数据
const activeTab = ref('basic')

// 适用车型表格配置
const vehicleColumns = [
  { title: '车型ID', key: 'vehicle_id', width: 120 },
  { title: '车型', key: 'vehicle_model', width: 200 },
  { title: '年份', key: 'year', width: 120 },
  { title: '发动机', key: 'engine', width: 120 },
  { title: '排量', key: 'displacement', width: 100 },
  { title: '功率', key: 'power', width: 100 },
]

const vehicleData = ref([
  {
    vehicle_id: 'Reach123',
    vehicle_model: '名爵MG3 1.3L AMT舒适版 11款',
    year: '2011-2012',
    engine: '13S4F',
    displacement: '1343',
    power: '68',
  },
  {
    vehicle_id: 'Reach123',
    vehicle_model: '名爵MG3 1.3L AMT舒适版 11款',
    year: '2011-2012',
    engine: '13S4F',
    displacement: '1343',
    power: '68',
  },
  {
    vehicle_id: 'Reach123',
    vehicle_model: '名爵MG3 1.3L AMT舒适版 11款',
    year: '2011-2012',
    engine: '13S4F',
    displacement: '1343',
    power: '68',
  },
])

const vehiclePagination = {
  pageSize: 10,
}

// 替代关系表格配置
const replacementColumns = [
  { title: 'OE号', key: 'oe_number', width: 150 },
  { title: '替代号', key: 'replacement_number', width: 150 },
  { title: '替代关系', key: 'relationship', width: 120 },
  { title: '替代备注', key: 'notes', width: 200 },
]

const replacementData = ref([
  {
    oe_number: '30008336',
    replacement_number: '10090902',
    relationship: '单向替代',
    notes: '断点时间2013.6',
  },
  {
    oe_number: '30008336',
    replacement_number: '10090909',
    relationship: '双向替代',
    notes: '生效时间2013.6',
  },
])

// Reach适配表格配置
const reachColumns = [
  { title: 'OE号', key: 'oe_number', width: 150 },
  { title: 'Reach号', key: 'reach_number', width: 150 },
  { title: '适配备注', key: 'notes', width: 300 },
]

const reachData = ref([
  {
    oe_number: '30008336',
    reach_number: '1.41.9999.126',
    notes: '不带柄，原版的优化版本',
  },
  {
    oe_number: '30008336',
    reach_number: '1.41.9499.126',
    notes: '原版的纤维版本',
  },
])
</script>

<style scoped>
.oe-detail-content {
  min-height: 600px;
}

.detail-tabs {
  padding: 0 24px;
}

.basic-info-section {
  padding: 30px 20px;
}

.info-group {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #666;
  padding: 8px 0;
}

.info-note {
  font-size: 12px;
  color: #999;
  font-style: italic;
}

.status-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.status-text {
  margin-left: 8px;
  font-size: 14px;
}

.project-section {
  margin-bottom: 20px;
}

.project-value {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  padding: 8px 0;
}

.notes-section {
  margin-top: 20px;
}

.notes-content {
  padding: 12px 0;
  line-height: 1.6;
}

.notes-content p {
  margin: 4px 0;
  font-size: 14px;
  color: #666;
}

.notes-footer {
  font-size: 12px;
  color: #999;
  font-style: italic;
  margin-top: 12px;
  line-height: 1.4;
}

.vehicles-section,
.replacement-section,
.reach-section {
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.replacement-table {
  margin-top: 16px;
}

/* 表格样式优化 */
:deep(.n-data-table) {
  border-radius: 4px;
}

:deep(.n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.n-tabs-nav) {
  border-bottom: 1px solid #e8e8e8;
}

:deep(.n-tabs-tab) {
  font-size: 16px;
  font-weight: 500;
}
</style>
