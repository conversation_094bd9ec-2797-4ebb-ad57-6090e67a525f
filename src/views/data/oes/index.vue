<template>
  <CommonPage>
    <!-- Search Section -->
    <n-grid :cols="20" :x-gap="12" class="mb-4">
      <n-gi :span="4">
        <n-form-item label="OE搜索" :label-width="60" label-placement="left">
          <n-input
            v-model:value="queryItems.oeSearch"
            placeholder="请输入OE号"
            clearable
            :disabled="loading"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="车型分类" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.vehicle_type"
            :options="vehicle_type_options"
            placeholder="车型分类"
            filterable
            clearable
            :loading="loading"
            :disabled="loading"
            @update:value="(v) => handleOptionChange('vehicle_type', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="品牌" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.brand"
            :options="brand_options"
            placeholder="品牌"
            filterable
            clearable
            :loading="loading"
            :disabled="loading"
            @update:value="(v) => handleOptionChange('brand', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="车系" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.series"
            :options="series_options"
            placeholder="车系"
            filterable
            clearable
            :loading="loading"
            :disabled="loading"
            @update:value="(v) => handleOptionChange('series', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="年款" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.model_year"
            :options="year_options"
            placeholder="年款"
            filterable
            clearable
            :loading="loading"
            :disabled="loading"
            @update:value="(v) => handleOptionChange('model_year', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="排量" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.displacement"
            :options="displacement_options"
            placeholder="排量"
            filterable
            clearable
            :loading="loading"
            :disabled="loading"
            @update:value="(v) => handleOptionChange('displacement', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="变速器类型" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.trans_type"
            :options="trans_options"
            placeholder="变速器类型"
            filterable
            clearable
            :loading="loading"
            :disabled="loading"
            @update:value="(v) => handleOptionChange('trans_type', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="燃油类型" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.fuel_type"
            :options="fuel_type_options"
            placeholder="燃油类型"
            filterable
            clearable
            :loading="loading"
            :disabled="loading"
            @update:value="(v) => handleOptionChange('fuel_type', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-button type="primary" @click="handleSearch">搜索</n-button>
        <n-button class="ml-2" @click="resetSearchForm">重置</n-button>
      </n-gi>
    </n-grid>

    <!-- OE数据主体 -->
    <QuerySelect
      ref="querySelectRef"
      class="mb-4"
      :options="tabOptions"
      @selected_value="handleCountryChange"
    />

    <!-- 表格筛选工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-right" style="width: 100%">
        <div style="display: flex; justify-content: flex-end; width: 100%">
          <n-popover trigger="click" placement="bottom-end" :show-arrow="false">
            <template #trigger>
              <n-button text class="display-btn">
                <template #icon>
                  <n-icon><UnorderedListOutlined /></n-icon>
                </template>
                字段显示
              </n-button>
            </template>
            <div class="column-select-popover">
              <n-checkbox-group v-model:value="checkedColumns">
                <n-space vertical>
                  <n-checkbox
                    v-for="option in columnOptions"
                    :key="option.key"
                    :value="option.key"
                    :label="option.label"
                  />
                </n-space>
              </n-checkbox-group>
            </div>
          </n-popover>
          <n-button text class="add-oe-btn">
            <template #icon>
              <n-icon><PlusCircleOutlined /></n-icon>
            </template>
            添加OE
          </n-button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="data-table-section">
      <n-data-table
        :columns="visibleColumns"
        :data="tableData"
        :pagination="pagination"
        :bordered="false"
        size="small"
        :scroll-x="1200"
      />
      <n-pagination
        v-model:page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :item-count="pagination.itemCount"
        :page-sizes="pagination.pageSizes"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>

    <!-- OE详情弹窗 -->
    <n-modal
      v-model:show="showDetailModal"
      preset="card"
      :style="{ width: '90vw', maxWidth: '1200px' }"
      title=""
      size="huge"
      :bordered="false"
      :segmented="{ content: true }"
    >
      <template #header>
        <div class="modal-header">
          <span class="modal-title">OE详情 {{ selectedOE?.oe_number || '' }}</span>
          <div class="modal-actions">
            <n-button quaternary size="small">Browse BOM</n-button>
            <n-button quaternary size="small">New Version</n-button>
            <n-button type="primary" size="small">Create</n-button>
          </div>
        </div>
      </template>

      <!-- 这里嵌入您之前的OE详情组件内容 -->
      <OEDetailContent :oe-data="selectedOE" />
    </n-modal>
  </CommonPage>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import {
  NInput,
  NSelect,
  NButton,
  NDataTable,
  NModal,
  NIcon,
  NTag,
  NDropdown,
  NPopover,
  NSpace,
  NCheckbox,
  NCheckboxGroup,
} from 'naive-ui'
import { UnorderedListOutlined, PlusCircleOutlined } from '@ant-design/icons-vue'
import OEDetailContent from './OEDetailContent.vue'
import CommonPage from '@/components/page/CommonPage.vue'
import QuerySelect from '@/components/query-bar/QuerySelect.vue'
import api from '@/api'

defineOptions({ name: 'OE数据查询' })

// 搜索表单数据
const queryItems = ref({
  country: null,
  oeSearch: '',
  vehicle_type: null,
  brand: null,
  series: null,
  model_year: null,
  displacement: null,
  trans_type: null,
  fuel_type: null,
})

// 当前选中的地区
const tabOptions = ref(['全部', '中国', '北美', '欧洲', '俄罗斯', '加拿大', '墨西哥', '东南亚'])
const querySelectRef = ref(null)

// 弹窗控制
const showDetailModal = ref(false)
const selectedOE = ref(null)

// 下拉选项数据
const vehicle_type_options = ref([])
const brand_options = ref([])
const series_options = ref([])
const year_options = ref([])
const displacement_options = ref([])
const trans_options = ref([])
const fuel_type_options = ref([])

const loading = ref(false)
const fetchInitOptions = async () => {
  loading.value = true
  try {
    const api_options = await api.getOptionList()
    vehicle_type_options.value = api_options.data.vehicle_type || []
    brand_options.value = api_options.data.brand || []
    series_options.value = api_options.data.series || []
    year_options.value = api_options.data.model_year || []
    displacement_options.value = api_options.data.displacement || []
    trans_options.value = api_options.data.trans_type || []
    fuel_type_options.value = api_options.data.fuel_type || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    loading.value = false // 完成加载
  }
}

const handleOptionChange = async (optionKey, selectedValue) => {
  loading.value = true
  try {
    queryItems.value[optionKey] = selectedValue
    // 从后端获取并更新其它选项
    const updatedOptions = await api.getOptionList(queryItems.value)
    // 更新选项
    vehicle_type_options.value = updatedOptions.data.vehicle_type || []
    brand_options.value = updatedOptions.data.brand || []
    series_options.value = updatedOptions.data.series || []
    year_options.value = updatedOptions.data.model_year || []
    displacement_options.value = updatedOptions.data.displacement || []
    trans_options.value = updatedOptions.data.trans_type || []
    fuel_type_options.value = updatedOptions.data.fuel_type || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    loading.value = false
  }
}

async function fetchData() {
  try {
    const params = {
      ...queryItems.value,
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
    }
    const res = await api.getCarsList(params)
    tableData.value = res.data
    pagination.value.itemCount = res.total
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

async function handleSearch() {
  pagination.value.page = 1
  await fetchData()
}

function handlePageChange(page) {
  pagination.value.page = page
  fetchData()
}

function handlePageSizeChange(pageSize) {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  fetchData()
}

const handleCountryChange = (value) => {
  queryItems.value.country = value === '全部' ? null : value
  handleOptionChange('country', queryItems.value.country)
  fetchData()
}

onMounted(() => {
  fetchInitOptions()
  handleSearch()
})

// 表格列配置
const baseColumns = [
  {
    title: '车型ID',
    key: 'vehicle_id',
    width: 120,
  },
  {
    title: 'K-Typ',
    key: 'k_typ',
    width: 100,
  },
  {
    title: '车型分类',
    key: 'vehicle_category',
    width: 120,
    render(row) {
      return h(NTag, { type: 'info', size: 'small' }, { default: () => row.vehicle_category })
    },
  },
  {
    title: 'OE',
    key: 'oe_number',
    width: 120,
    render(row) {
      return h(
        'span',
        {
          style: 'font-weight: 600; color: #2080f0; cursor: pointer;',
          onClick: () => openDetailModal(row),
        },
        row.oe_number
      )
    },
  },
  {
    title: '安装位置',
    key: 'installation_position',
    width: 150,
  },
  {
    title: '用量',
    key: 'quantity',
    width: 80,
    align: 'center',
  },
  {
    title: '起止时间',
    key: 'time_period',
    width: 150,
  },
]

// 操作列（始终显示）
const actionColumn = {
  title: '操作',
  key: 'actions',
  width: 100,
  align: 'center',
  render(row) {
    return h(
      NDropdown,
      {
        trigger: 'click',
        options: [
          {
            label: '查看详情',
            key: 'detail',
            props: {
              onClick: () => openDetailModal(row),
            },
          },
          {
            label: '编辑',
            key: 'edit',
          },
          {
            label: '删除',
            key: 'delete',
          },
        ],
      },
      {
        default: () =>
          h(
            NButton,
            {
              text: true,
              size: 'small',
              style: 'padding: 4px 8px;',
            },
            {
              default: () => '⋯',
            }
          ),
      }
    )
  },
}

// 字段显示选项
const columnOptions = ref([
  { label: '车型ID', key: 'vehicle_id', checked: true },
  { label: 'K-Typ', key: 'k_typ', checked: true },
  { label: '车型分类', key: 'vehicle_category', checked: true },
  { label: 'OE', key: 'oe_number', checked: true },
  { label: '安装位置', key: 'installation_position', checked: true },
  { label: '用量', key: 'quantity', checked: true },
  { label: '起止时间', key: 'time_period', checked: true },
])

// 选中的列
const checkedColumns = ref(columnOptions.value.map((opt) => opt.key))

// 计算可见列
const visibleColumns = computed(() => {
  const columns = baseColumns.filter((col) => checkedColumns.value.includes(col.key))
  columns.push(actionColumn)
  return columns
})

// 表格数据
const tableData = ref([])

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
})

// 打开详情弹窗
const openDetailModal = (row) => {
  selectedOE.value = {
    ...row,
    oe_number: '30008336',
    oe_name: '发动机散热器',
    manufacturer: 'Denso',
    material: 'Aluminium',
    production_number: '4580-249',
    position: '冷却散热风扇模块',
  }
  showDetailModal.value = true
}

// 重置搜索表单
async function resetSearchForm() {
  queryItems.value = {
    country: null,
    oeSearch: '',
    vehicle_type: null,
    brand: null,
    series: null,
    model_year: null,
    displacement: null,
    trans_type: null,
    fuel_type: null,
  }
  if (querySelectRef.value) {
    querySelectRef.value.selectTab(tabOptions.value[0]) // 选中"全部"
  }
  // 重新填充所有下拉框的数据
  await fetchInitOptions()
  await handleSearch()
}
</script>

<style scoped>
.search-section {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.search-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.search-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-label {
  font-size: 13px;
  font-weight: 500;
  color: #606266;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.search-btn,
.advanced-search-btn,
.reset-btn {
  min-width: 90px;
}

.oe-data-main {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.data-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2d3d;
  margin: 0;
}

.region-tabs {
  flex: 1;
  margin-left: 20px;
}

.table-toolbar {
  display: flex;
  justify-content: flex-end; /* 确保整个工具栏内容右对齐 */
  margin-bottom: 16px;
}

.toolbar-right {
  width: 100%;
}

/* 按钮间距调整 */
.display-btn,
.add-oe-btn {
  margin-left: 10px;
}

.display-btn,
.add-oe-btn {
  padding: 0 12px;
  height: 32px;
  display: flex;
  align-items: center;
}

.data-table-section {
  margin-bottom: 20px;
}

.oe-data-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.footer-right {
  display: flex;
  align-items: center;
}

.pagination-prefix {
  font-size: 13px;
  color: #606266;
  margin-right: 15px;
}

.column-select-popover {
  padding: 8px 0;
  min-width: 120px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
}

.modal-actions {
  display: flex;
  gap: 10px;
}

:deep(.n-tabs-tab) {
  padding: 8px 16px;
  font-size: 14px;
}

:deep(.n-tabs-tab--active) {
  font-weight: 600;
  color: #2080f0;
}

:deep(.n-tabs-bar) {
  background-color: #2080f0;
}

:deep(.n-data-table-th) {
  background-color: #f8f9fc;
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 12px 16px;
}
</style>
