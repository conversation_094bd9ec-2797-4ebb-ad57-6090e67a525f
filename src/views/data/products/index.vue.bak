<script setup>
import { nextTick, ref } from 'vue'
import { NInput, NButton, NDataTable, NModal, NGrid, NGi, useMessage } from 'naive-ui'
import { AddCircleOutline, Close, Add } from '@vicons/ionicons5'
import CommonPage from '@/components/page/CommonPage.vue'
import api from '@/api'

defineOptions({ name: '产品数据查询' })

const message = useMessage()

// 搜索相关状态
const pageSize = 20
const currentPage = ref(1)

const searchQuery = ref('')
const ProductList = ref([])
const isSearching = ref(false)

// 计算属性
const totalPages = computed(() => Math.ceil(ProductList.value.length / pageSize))
const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  return ProductList.value.slice(start, start + pageSize)
})

// 表格列配置
const productcolumns = ref([
  {
    title: '物料代码',
    key: 'MaterialCode',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '主号',
    key: 'MainNo',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '品牌',
    key: 'Brand',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '中文名称',
    key: 'ChineseName',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '规格型号',
    key: 'SpecModel',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '属性',
    key: 'Attributes',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '可供状态',
    key: 'Availability',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '适用市场',
    key: 'Market',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车系英文',
    key: 'SeriesEN',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型英文',
    key: 'ModelEN',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车系中文',
    key: 'SeriesCN',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型中文',
    key: 'ModelCN',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '内销OE',
    key: 'DomesticOE',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '内销标签车型',
    key: 'DomesticLabelModel',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '有无照片',
    key: 'HasPhoto',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '是否同步007',
    key: 'Sync007',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '007优良等级',
    key: 'QualityLevel007',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '参考号',
    key: 'ReferenceNo',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '是否做目录',
    key: 'IsCataloged',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '是否新品发布',
    key: 'IsNewRelease',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '新品发布日期',
    key: 'NewReleaseDate',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: 'EAN号',
    key: 'EAN',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: 'HS编码',
    key: 'HSCode',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '长',
    key: 'Length',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '宽',
    key: 'Width',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '高',
    key: 'Height',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '体积',
    key: 'Volume',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '替代说明',
    key: 'ReplacementDesc',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '供应商名称',
    key: 'SupplierName',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '默认供应商ID',
    key: 'DefaultSupplierID',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '备注',
    key: 'Remarks',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '更多',
    key: 'actions',
    align: 'center',
    width: 'auto',
    fixed: 'right',
    render(row) {
      return h(
        NButton,
        {
          size: 'small',
          onClick: () => Detail(row),
        },
        { default: () => 'Detail' }
      )
    },
  },
])

const carscolumns = [
  {
    title: '品牌',
    key: 'brand',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '厂家',
    key: 'manufacturer',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型分类',
    key: 'vehicle_type',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车系',
    key: 'series',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型',
    key: 'model',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '年款',
    key: 'model_year',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '排量',
    key: 'displacement',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: 'Vio',
    key: 'vio',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
]

// 搜索OE列表
const searchProductList = async () => {
  isSearching.value = true
  let loadingMessage = null
  if (!searchQuery.value) {
    message.error('请输入搜索条件')
    isSearching.value = false
    return
  }
  try {
    loadingMessage = message.loading('搜索中...', { duration: 0 })
    const res = await api.searchProduct({ query_str: searchQuery.value })
    if (res.data?.length === 0) {
      message.warning('未找到相关数据', { duration: 2000 })
      return
    }
    ProductList.value = res.data
  } catch (error) {
    message.error('搜索失败，请稍后重试')
  } finally {
    isSearching.value = false
    if (loadingMessage) loadingMessage.destroy()
  }
}

// 标准名称与字段映射配置
const standardLabelConfigs = {
  水箱: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '芯体长（英寸）', key: '芯体长（英寸）' },
    { label: '芯体宽（英寸）', key: '芯体宽（英寸）' },
    { label: '芯体厚（英寸）', key: '芯体厚（英寸）' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '水室方向', key: '水室方向' },
    { label: 'TOC规格', key: 'TOC规格' },
    { label: 'TOC油冷器中心距mm', key: 'TOC油冷器中心距mm' },
    { label: 'EOC规格', key: 'EOC规格' },
    { label: 'EOC油冷器中心距mm', key: 'EOC油冷器中心距mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: 'AT/MT', key: 'AT/MT' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '进口管直径mm(螺纹)', key: '进口管直径mm(螺纹)' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '出口管直径mm(螺纹)', key: '出口管直径mm(螺纹)' },
    { label: '芯体排数', key: '芯体排数' },
    { label: '是否带加水口', key: '是否带加水口' },
    { label: '是否带盖子', key: '是否带盖子' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  冷凝器: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '进口结构', key: '进口结构' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口结构', key: '出口结构' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '是否含干燥瓶', key: '是否含干燥瓶' },
    { label: '干燥瓶类型', key: '干燥瓶类型' },
    { label: '是否带干燥剂', key: '是否带干燥剂' },
    { label: '是否带储液器', key: '是否带储液器' },
    { label: '储液器类型', key: '储液器类型' },
    { label: '是否双系统油散', key: '是否双系统油散' },
    { label: '双系统油散进口尺寸', key: '双系统油散进口尺寸' },
    { label: '双系统油散出口尺寸', key: '双系统油散出口尺寸' },
    { label: '制冷剂', key: '制冷剂' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  暖风: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '是否带管子', key: '是否带管子' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  中冷器: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '冷却方式', key: '冷却方式' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  蒸发器: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '进口结构', key: '进口结构' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口结构', key: '出口结构' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  压缩机: [
    { label: '电压【V】', key: '电压【V】' },
    { label: '插头结构', key: '插头结构' },
    { label: '皮带轮类型', key: '皮带轮类型' },
    { label: '沟槽的数量', key: '沟槽的数量' },
    { label: '皮带盘直径mm', key: '皮带盘直径mm' },
    { label: '特定生产商', key: '特定生产商' },
    { label: '压缩机-ID', key: '压缩机-ID' },
    { label: '预充 PAG 油', key: '预充 PAG 油' },
    { label: '压缩机机油', key: '压缩机机油' },
    { label: '机油加注量ml', key: '机油加注量ml' },
    { label: '排量-压缩机CC', key: '排量-压缩机CC' },
    { label: '制冷剂', key: '制冷剂' },
    { label: '固定方式', key: '固定方式' },
    { label: '固定孔的数量', key: '固定孔的数量' },
    { label: '离合器连接器性别', key: '离合器连接器性别' },
    { label: '连接器数量', key: '连接器数量' },
    { label: '终端数量', key: '终端数量' },
    { label: '包括离合器', key: '包括离合器' },
    { label: '包含交换机', key: '包含交换机' },
    { label: '交换机服务端口', key: '交换机服务端口' },
    { label: '线圈时钟位置', key: '线圈时钟位置' },
    { label: '线圈数量', key: '线圈数量' },
    { label: '旋转方向', key: '旋转方向' },
    { label: '是否含O型圈', key: '是否含O型圈' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
    { label: 'C-O型圈规格', key: 'C-O型圈规格' },
    { label: 'C-O型圈数量', key: 'C-O型圈数量' },
    { label: 'C-O型圈位置', key: 'C-O型圈位置' },
    { label: 'D-O型圈规格', key: 'D-O型圈规格' },
    { label: 'D-O型圈数量', key: 'D-O型圈数量' },
    { label: 'D-O型圈位置', key: 'D-O型圈位置' },
    { label: '安装孔上下间距mm', key: '安装孔上下间距mm' },
    { label: '安装孔左右间距mm', key: '安装孔左右间距mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  传感器: [
    { label: '电压【V】', key: '电压【V】' },
    { label: '插头结构', key: '插头结构' },
    { label: '插塞接头数量', key: '插塞接头数量' },
    { label: '长度mm', key: '长度mm' },
    { label: '材质', key: '材质' },
    { label: '颜色', key: '颜色' },
    { label: '传感器类型', key: '传感器类型' },
    { label: '安装位置', key: '安装位置' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  干燥瓶: [
    { label: '干燥瓶类型', key: '干燥瓶类型' },
    { label: '是否带干燥剂', key: '是否带干燥剂' },
    { label: '是否符合OEM', key: '是否符合OEM' },
    { label: '过滤材料', key: '过滤材料' },
    { label: '保险头的材质', key: '保险头的材质' },
    { label: '直径MM', key: '直径MM' },
    { label: '长度mm', key: '长度mm' },
    { label: '进口结构', key: '进口结构' },
    { label: '进口管直径mm(螺纹)', key: '进口管直径mm(螺纹)' },
    { label: '出口结构', key: '出口结构' },
    { label: '出口管直径mm(螺纹)', key: '出口管直径mm(螺纹)' },
    { label: '连接口尺寸', key: '连接口尺寸' },
    { label: '是否有保险', key: '是否有保险' },
    { label: '是否包含垫片', key: '是否包含垫片' },
    { label: '是否带支架', key: '是否带支架' },
    { label: '是否带管子', key: '是否带管子' },
    { label: '是否有安装硬件', key: '是否有安装硬件' },
    { label: '是否有减压阀', key: '是否有减压阀' },
    { label: '是否有安装衬垫', key: '是否有安装衬垫' },
    { label: '交换机服务端口', key: '交换机服务端口' },
    { label: '终端数量', key: '终端数量' },
    { label: '是否有螺丝/钉子', key: '是否有螺丝/钉子' },
    { label: '开关口尺寸MM', key: '开关口尺寸MM' },
    { label: '是否有开关', key: '是否有开关' },
    { label: '开关数量', key: '开关数量' },
    { label: '是否有观察的玻璃镜', key: '是否有观察的玻璃镜' },
    { label: '是否有玻璃镜', key: '是否有玻璃镜' },
    { label: '是否含O型圈', key: '是否含O型圈' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
    { label: 'C-O型圈规格', key: 'C-O型圈规格' },
    { label: 'C-O型圈数量', key: 'C-O型圈数量' },
    { label: 'C-O型圈位置', key: 'C-O型圈位置' },
    { label: 'D-O型圈规格', key: 'D-O型圈规格' },
    { label: 'D-O型圈数量', key: 'D-O型圈数量' },
    { label: 'D-O型圈位置', key: 'D-O型圈位置' },
    { label: '铝制插头规格', key: '铝制插头规格' },
    { label: '塑料插头规格', key: '塑料插头规格' },
    { label: '塑料网规格', key: '塑料网规格' },
    { label: '塑料头规格', key: '塑料头规格' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  膨胀阀: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '材质', key: '材质' },
    { label: '压力(bar)', key: '压力(bar)' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '膨胀阀类型', key: '膨胀阀类型' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '进口管直径(英寸)', key: '进口管直径(英寸)' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '出口管直径(英寸)', key: '出口管直径(英寸)' },
    { label: '吸入口类型', key: '吸入口类型' },
    { label: '进口结构', key: '进口结构' },
    { label: '进气口直径mm', key: '进气口直径mm' },
    { label: '进气口直径(英寸)', key: '进气口直径(英寸)' },
    { label: '出口结构', key: '出口结构' },
    { label: '岀气口直径(mm)', key: '岀气口直径(mm)' },
    { label: '岀气口直径(英寸)', key: '岀气口直径(英寸)' },
    { label: '毛细管长度(mm)', key: '毛细管长度(mm)' },
    { label: '毛细管温包长度(mm)', key: '毛细管温包长度(mm)' },
    { label: '外平衡管毛细管长度(mm)', key: '外平衡管毛细管长度(mm)' },
    { label: '外平衡管尺寸(mm)', key: '外平衡管尺寸(mm)' },
    { label: '外平衡管尺寸(英寸)', key: '外平衡管尺寸(英寸)' },
    { label: '是否含O型圈', key: '是否含O型圈' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
    { label: 'C-O型圈规格', key: 'C-O型圈规格' },
    { label: 'C-O型圈数量', key: 'C-O型圈数量' },
    { label: 'C-O型圈位置', key: 'C-O型圈位置' },
    { label: 'D-O型圈规格', key: 'D-O型圈规格' },
    { label: 'D-O型圈数量', key: 'D-O型圈数量' },
    { label: 'D-O型圈位置', key: 'D-O型圈位置' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  电子扇: [
    { label: '电压【V】', key: '电压【V】' },
    { label: '额定功率【W】', key: '额定功率【W】' },
    { label: '直径MM', key: '直径MM' },
    { label: '插头结构', key: '插头结构' },
    { label: '插塞接头数量', key: '插塞接头数量' },
    { label: '通风机叶片的数量', key: '通风机叶片的数量' },
    { label: '是否带控制模块', key: '是否带控制模块' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  default: [],
}

// 在setup() 中添加滚动方法
const scrollTo = (id) => {
  const container = document.querySelector('.n-card__content [style*="overflow-y"]')
  const target = document.getElementById(id)

  if (container && target) {
    // 计算容器内相对位置
    const targetTop = target.offsetTop - container.offsetTop - 20
    container.scrollTo({
      top: targetTop,
      behavior: 'smooth',
    })
  }
}

const showModal = ref(false)
const currentStandardLabel = ref('')
const productDetailData = ref({})
const carsdata = ref([])
const allviocount = ref(0)

const Detail = async (rowData) => {
  try {
    if (!rowData.Materiel_Number || rowData.Materiel_Number === '') {
      productDetailData.value = {}
    } else {
      const prod_res = await api.getProductDetail({
        group_number: rowData.Materiel_Number,
      })
      productDetailData.value = prod_res.data
    }

    const cars_res = await api.searchCarsByProduct({
      product: rowData.Materiel_Number,
    })
    carsdata.value = cars_res.data
    allviocount.value = carsdata.value.reduce((acc, item) => acc + (Number(item.vio) || 0), 0)

    currentStandardLabel.value = rowData.part_name
    showModal.value = true
  } catch (error) {
    message.error('获取OE详情失败')
  }
}

// 计算当前配置
const currentConfig = computed(() => {
  return standardLabelConfigs[currentStandardLabel.value] || standardLabelConfigs.default
})

// =============================================================================================================

// 添加验证状态
const formRef = ref(null)
const basicRules = {
  category: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择品类',
  },
  market: {
    required: true,
    trigger: 'blur',
    message: '请输入市场',
  },
  suffix: {
    required: true,
    trigger: 'blur',
    message: '请输入尾缀',
  },
}

const onlyAllowNumberLetter = (value) => !value || /^[0-9A-Za-z]+$/.test(value)

// 添加失去焦点验证
// const handleBlur = (field) => {
//   formRef.value?.validate([field])
// }

// 新增状态 - 产品创建
const showCreateModal = ref(false)
const currentStep = ref(1)
const formData = ref({
  basic: {
    category: null,
    market: null,
    suffix: null,
  },
  reference: {
    oe: '',
    brands: [],
    series: '',
    model: '',
  },
  logistics: {
    supplier: '',
    supplierCode: '',
    price: null,
    coreLength: null,
    coreWidth: null,
    coreHeight: null,
    netWeight: null,
    grossWeight: null,
    mpq: null,
    moq: null,
    boxLength: null,
    boxWidth: null,
    boxHeight: null,
    boxGrossWeight: null,
  },
  params: {},
})
const article_brand_type = ref(['4SEASONS', 'AVA', 'DPI', 'Dorman', 'NISSENS', 'NRF', 'UAC'])
const categories = ref([
  '水箱',
  '冷凝器',
  '暖风',
  '中冷器',
  '蒸发器',
  '压缩机',
  '传感器',
  '干燥瓶',
  '膨胀阀',
  '电子扇',
])
const referenceBrands = ref([{ brand: '', number: '' }])
const logisticsList = ref([
  {
    supplier: '',
    code: '',
    price: null,
    innerLength: null,
    innerWidth: null,
    innerHeight: null,
    netWeight: null,
    grossWeight: null,
    mpq: null,
    moq: null,
    boxLength: null,
    boxWidth: null,
    boxHeight: null,
    boxGrossWeight: null,
  },
])
// const existingProductData = ref(null)

// 新增方法 - 产品创建
const openCreate = () => {
  showCreateModal.value = true
  currentStep.value = 1
  formData.value = {
    basic: {
      category: null,
      market: null,
      suffix: null,
    },
    reference: {
      oe: '',
      brands: [{ brand: '', number: '' }], // 初始化品牌数组
      series: '',
      model: '',
    },
    params: {},
  }
  logisticsList.value = [
    {
      supplier: '',
      code: '',
      price: null,
      innerLength: null,
      innerWidth: null,
      innerHeight: null,
      netWeight: null,
      grossWeight: null,
      mpq: null,
      moq: null,
      boxLength: null,
      boxWidth: null,
      boxHeight: null,
      boxGrossWeight: null,
    },
  ]
}

// 新增供应商方法
const addSupplier = () => {
  logisticsList.value.push({
    supplier: '',
    code: '',
    price: null,
    innerLength: null,
    innerWidth: null,
    innerHeight: null,
    netWeight: null,
    grossWeight: null,
    mpq: null,
    moq: null,
    boxLength: null,
    boxWidth: null,
    boxHeight: null,
    boxGrossWeight: null,
  })
}

const removeSupplier = (index) => {
  if (logisticsList.value.length > 1) {
    logisticsList.value.splice(index, 1)
  }
}

const handleNext = async () => {
  // 第一步验证
  if (currentStep.value === 1) {
    try {
      await formRef.value?.validate()
    } catch (errors) {
      message.error('请先完善基础信息')
      return
    }
  }

  // 第二步验证产品是否存在
  if (currentStep.value === 2) {
    try {
      const res = await api.checkProductExist(formData.value.reference)
      if (res.data?.length > 0) {
        existingProducts.value = res.data
        showExistModal.value = true
        return
      }
    } catch (error) {
      message.error('检查产品失败')
      return
    }
  }

  if (currentStep.value < 4) currentStep.value++
}

const handlePrev = () => {
  if (currentStep.value > 1) currentStep.value--
}

const lastBrandRef = ref(null)
const addBrand = async () => {
  formData.value.reference.brands.push({ brand: '', number: '' })
  await nextTick()
  if (lastBrandRef.value) {
    lastBrandRef.value.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
    })
  }
}

const removeBrand = (index) => {
  formData.value.reference.brands.splice(index, 1)
}

const submitProduct = async () => {
  try {
    await api.createProduct({
      ...formData.value.basic,
      ...formData.value.reference,
      referenceBrands: referenceBrands.value,
      ...formData.value.logistics,
      ...formData.value.params,
    })
    message.success('创建成功')
    showCreateModal.value = false
  } catch (error) {
    message.error('创建失败')
  }
}

const showExistModal = ref(false)
const existingProducts = ref([])

const showDetail = (row) => {
  Detail(row)
  showExistModal.value = false
}

// 新增存在产品表格列配置
const existColumns = [
  {
    title: '物料编码',
    key: 'Materiel_Number',
    render(row) {
      return h(
        'a',
        {
          class: 'text-blue-500 hover:cursor-pointer',
          onClick: () => showDetail(row),
        },
        row.Materiel_Number
      )
    },
  },
  {
    title: '零件名称',
    key: 'part_name',
  },
]

// 新增产品参数状态
const productParams = ref({})

// 计算参数配置
const productParamConfig = computed(() => {
  return standardLabelConfigs[formData.value.basic.category] || []
})
</script>

<template>
  <CommonPage>
    <!-- 搜索区域 -->
    <n-grid :cols="10" :x-gap="12" class="mb-4">
      <n-gi :span="6">
        <n-input
          v-model:value="searchQuery"
          placeholder="输入产品编码进行搜索"
          clearable
          :style="{ width: '45%' }"
          @keyup.enter="searchProductList"
        />
        <n-button type="primary" :loading="isSearching" @click="searchProductList">搜索</n-button>
      </n-gi>
      <n-gi :span="4" class="text-right">
        <n-button type="primary" @click="openCreate">新建产品</n-button>
      </n-gi>
    </n-grid>

    <!-- 搜索结果表格 -->
    <n-data-table :columns="productcolumns" :data="currentPageData" :bordered="true" class="mb-4" />
    <n-pagination
      v-model:page="currentPage"
      :page-count="totalPages"
      @update:page="handlePageChange"
    />

    <n-modal v-model:show="showExistModal" :style="{ width: '600px' }">
      <n-card title="系统已存在相同产品" content-style="padding: 16px;">
        <n-data-table :columns="existColumns" :data="existingProducts" :bordered="false" />
        <template #footer>
          <n-button type="primary" @click="showExistModal = false">关闭</n-button>
        </template>
      </n-card>
    </n-modal>

    <n-modal v-model:show="showCreateModal" preset="dialog" :style="{ width: '850px' }">
      <n-card title="新建产品" content-style="padding: 16px; max-height: 70vh;">
        <!-- 步骤导航 -->
        <div class="steps-nav mb-6">
          <n-text :class="{ 'font-bold': currentStep === 1 }">基础信息</n-text>
          <n-text class="mx-2">></n-text>
          <n-text :class="{ 'font-bold': currentStep === 2 }">参考信息</n-text>
          <n-text class="mx-2">></n-text>
          <n-text :class="{ 'font-bold': currentStep === 3 }">物流信息</n-text>
          <n-text class="mx-2">></n-text>
          <n-text :class="{ 'font-bold': currentStep === 4 }">产品参数</n-text>
        </div>

        <!-- 步骤内容 -->
        <div v-show="currentStep === 1">
          <n-form
            ref="formRef"
            :model="formData.basic"
            :rules="basicRules"
            label-placement="left"
            label-width="auto"
          >
            <n-form-item label="品类" path="category" required>
              <n-select
                v-model:value="formData.basic.category"
                :options="categories.map((c) => ({ label: c, value: c }))"
              />
            </n-form-item>
            <n-form-item label="市场" path="market" required>
              <n-input v-model:value="formData.basic.market" />
            </n-form-item>
            <n-form-item label="尾缀" path="suffix" required>
              <n-input
                v-model:value="formData.basic.suffix"
                :allow-input="onlyAllowNumberLetter"
                placeholder="请输入英文/数字"
              />
            </n-form-item>
          </n-form>
        </div>

        <div v-show="currentStep === 2">
          <n-form label-placement="left" label-width="140px">
            <n-grid :cols="24" :x-gap="24">
              <!-- OE 字段 -->
              <n-gi :span="24">
                <n-form-item label="OE" label-style="font-weight: bold; font-size: 14px">
                  <n-input
                    v-model:value="formData.reference.oe"
                    placeholder="请输入OE, 以';'分隔多个"
                  />
                </n-form-item>
              </n-gi>

              <!-- 参考品牌区域 -->
              <n-gi :span="24">
                <n-divider title-placement="left" class="full-width-divider">
                  <span style="font-weight: bold; font-size: 14px">参考品牌</span>
                  <n-button size="small" type="primary" class="add-brand-btn" @click="addBrand">
                    <template #icon>
                      <n-icon><AddCircleOutline /></n-icon>
                    </template>
                    添加品牌
                  </n-button></n-divider
                >
              </n-gi>

              <!-- 品牌列表容器 -->
              <n-gi :span="24">
                <div class="brand-list-container">
                  <div
                    v-for="(brand, index) in formData.reference.brands"
                    :key="index"
                    :ref="
                      (el) => {
                        if (index === formData.reference.brands.length - 1) lastBrandRef = el
                      }
                    "
                    class="brand-item"
                  >
                    <n-grid :cols="24" :x-gap="12">
                      <n-gi :span="10">
                        <n-form-item label="品牌名称" required>
                          <n-select
                            v-model:value="brand.brand"
                            :options="article_brand_type.map((c) => ({ label: c, value: c }))"
                          />
                        </n-form-item>
                      </n-gi>
                      <n-gi :span="10">
                        <n-form-item label="参考号" required>
                          <n-input v-model:value="brand.number" />
                        </n-form-item>
                      </n-gi>
                      <n-gi :span="4">
                        <n-button circle type="error" secondary @click="removeBrand(index)">
                          <template #icon>
                            <n-icon><Close /></n-icon>
                          </template>
                        </n-button>
                      </n-gi>
                    </n-grid>
                  </div>
                </div>
              </n-gi>

              <!-- 适用车系/车型 -->
              <n-gi :span="24">
                <n-form-item label="适用车系" label-style="font-weight: bold; font-size: 14px">
                  <n-input v-model:value="formData.reference.series" />
                </n-form-item>
                <n-form-item label="适用车型" label-style="font-weight: bold; font-size: 14px">
                  <n-input v-model:value="formData.reference.model" />
                </n-form-item>
              </n-gi>
            </n-grid>
          </n-form>
        </div>

        <div v-show="currentStep === 3">
          <n-form label-placement="top">
            <div v-for="(item, index) in logisticsList" :key="index" class="supplier-item">
              <n-space vertical>
                <div class="mb-4 flex items-center justify-between">
                  <n-text strong>供应商 {{ index + 1 }}</n-text>
                  <n-button
                    v-if="index > 0"
                    circle
                    size="small"
                    type="error"
                    @click="removeSupplier(index)"
                  >
                    <template #icon>
                      <n-icon><Close /></n-icon>
                    </template>
                  </n-button>
                </div>

                <n-grid :cols="12" :x-gap="18" :y-gap="12">
                  <n-gi :span="4">
                    <n-form-item label="供应商名称" required>
                      <n-input v-model:value="item.supplier" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="4">
                    <n-form-item label="供应商编码" required>
                      <n-input v-model:value="item.code" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="4">
                    <n-form-item label="含税价（元）" required>
                      <n-input-number v-model:value="item.price" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="4">
                    <n-form-item label="内盒长（mm）" required>
                      <n-input-number v-model:value="item.innerLength" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="4">
                    <n-form-item label="内盒宽（mm）" required>
                      <n-input-number v-model:value="item.innerWidth" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="4">
                    <n-form-item label="内盒高（mm）" required>
                      <n-input-number v-model:value="item.innerHeight" :min="0" />
                    </n-form-item>
                  </n-gi>

                  <n-gi :span="3">
                    <n-form-item label="单品净重（kg）" required>
                      <n-input-number v-model:value="item.netWeight" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="单品毛重（kg）" required>
                      <n-input-number v-model:value="item.grossWeight" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="MPQ" required>
                      <n-input-number v-model:value="item.mpq" :min="1" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="MOQ" required>
                      <n-input-number v-model:value="item.moq" :min="1" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="外箱长（mm）" required>
                      <n-input-number v-model:value="item.boxLength" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="外箱宽（mm）" required>
                      <n-input-number v-model:value="item.boxWidth" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="外箱高（mm）" required>
                      <n-input-number v-model:value="item.boxHeight" :min="0" />
                    </n-form-item>
                  </n-gi>
                  <n-gi :span="3">
                    <n-form-item label="整箱毛重（kg）" required>
                      <n-input-number v-model:value="item.boxGrossWeight" :min="0" />
                    </n-form-item>
                  </n-gi>
                </n-grid>
              </n-space>
            </div>

            <div class="mt-4 text-center">
              <n-button type="primary" @click="addSupplier">
                <template #icon>
                  <n-icon><Add /></n-icon>
                </template>
                添加供应商
              </n-button>
            </div>
          </n-form>
        </div>

        <div v-show="currentStep === 4">
          <n-form label-placement="top">
            <n-grid :cols="3" :x-gap="24" :y-gap="16">
              <n-gi v-for="(field, index) in productParamConfig" :key="index">
                <n-form-item :label="field.label">
                  <n-input
                    v-model:value="productParams[field.key]"
                    :placeholder="`请输入${field.label}`"
                  />
                </n-form-item>
              </n-gi>
            </n-grid>
          </n-form>
        </div>

        <!-- 操作按钮 -->
        <template #footer>
          <div class="flex justify-end gap-4">
            <n-button @click="currentStep > 1 ? handlePrev() : (showCreateModal = false)">
              {{ currentStep > 1 ? '上一步' : '取消' }}
            </n-button>
            <n-button
              type="primary"
              :disabled="currentStep === 1 && !formRef?.validate"
              @click="currentStep === 4 ? submitProduct() : handleNext()"
            >
              {{ currentStep === 4 ? '完成' : '下一步' }}
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>

    <NModal v-model:show="showModal" preset="dialog" :style="{ width: '900px' }" title="详情信息">
      <NCard
        content-style="padding: 16px; max-height: 70vh; display: flex; flex-direction: column;"
      >
        <!-- 导航选项卡 -->
        <n-tabs
          type="line"
          default-value="vehicle"
          size="medium"
          style="margin-bottom: 16px; position: sticky; top: 0; background: white; z-index: 1"
        >
          <n-tab name="vehicle" @click="scrollTo('product-details')">产品属性详情</n-tab>
          <n-tab name="oe" @click="scrollTo('vehicle-details')">车型详情</n-tab>
        </n-tabs>

        <!-- 内容容器 -->
        <div style="flex: 1; overflow-y: auto; padding: 0 8px">
          <!-- 车型详情 -->
          <n-card
            id="product-details"
            title="产品属性详情"
            size="small"
            :bordered="false"
            style="margin-bottom: 16px"
          >
            <n-descriptions bordered :column="2" label-placement="left">
              <n-descriptions-item label="标准名称" label-style="font-weight: bold;">
                {{ currentStandardLabel }}
              </n-descriptions-item>

              <template v-for="item in currentConfig" :key="item.key">
                <n-descriptions-item :label="item.label" label-style="font-weight: bold;">
                  {{ productDetailData[item.key] || '-' }}
                </n-descriptions-item>
              </template>
            </n-descriptions>
          </n-card>

          <!-- OE详情 -->
          <n-card
            id="vehicle-details"
            title="车型详情"
            size="small"
            :bordered="false"
            style="margin-bottom: 16px"
          >
            <n-data-table
              :columns="carscolumns"
              :data="carsdata"
              :max-height="250"
              virtual-scroll
              :row-props="rowProps"
            />
            <div
              style="text-align: center; margin-bottom: 20px; font-size: 18px; font-weight: bold"
            >
              vio总量: {{ allviocount }}
            </div>
          </n-card>
        </div>
      </NCard>
    </NModal>
  </CommonPage>
</template>

<style>
.n-form-item-label {
  white-space: nowrap;
  font-size: 13px;
}

.n-input .n-input-number {
  width: 100% !important;
}

/* 表格悬停效果 */
.n-data-table-tr--hover {
  background-color: #f8f8f8;
  transition: background-color 0.2s;
}

/* 弹窗滚动条样式 */
.n-card__content::-webkit-scrollbar {
  width: 6px;
}
.n-card__content::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 3px;
}
.steps-nav {
  @apply flex items-center text-gray-600;
  font-size: 16px;
}
.font-bold {
  font-weight: 600;
  color: #333;
}
.brand-item {
  padding: 12px 8px;
  margin-bottom: 16px;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  transition: all 0.2s;
  margin: 0 8px 12px 0;
  box-sizing: border-box;
}

.n-grid {
  margin: 0 !important; /* 移除栅格系统外边距 */
  padding: 0 6px !important;
}

.brand-item:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}
.brand-list-container {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
  margin: 12px -8px 12px 0;
  padding-right: 8px;
}

/* 调整删除按钮对齐 */
.n-form-item .n-input-group {
  align-items: center;
}

.n-form-item .n-input {
  min-width: auto !important; /* 移除输入框最小宽度限制 */
}

.n-grid--x-gap-12 {
  gap: 0 12px !important; /* 保持垂直间距，调整水平间距 */
  margin: 0 -6px !important;
  width: calc(100% + 12px);
}

.n-form-item .n-form-item-label {
  width: 80px !important;
  justify-content: flex-start !important;
}

.n-modal .n-card {
  max-height: 90vh !important; /* 增加可视区域 */
  display: flex;
  flex-direction: column;
}

/* 品牌项间距优化 */
.brand-item {
  margin-bottom: 12px;
  padding: 12px;
}

/* Dialog容器高度控制 */
.n-card {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.n-card__content {
  flex: 1;
  overflow: auto !important;
  display: flex;
  flex-direction: column;
}

/* 统一布局样式 */
.n-form-item .n-form-item-label {
  width: 80px !important;
  justify-content: flex-start !important;
}

.brand-list-container {
  overflow-x: hidden;
  max-height: 200px;
  overflow-y: auto;
  margin: 12px 0;
  padding-right: 8px;
}

.full-width-divider {
  width: calc(100% + 40px);
  margin-left: -20px;
  padding: 0 20px;
}

.add-brand-btn {
  position: absolute;
  right: 24px;
  transform: translateY(-50%);
}

.brand-item {
  margin-bottom: 12px;
  padding: 12px;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  background: #f8f8f8;
}

/* 固定底部表单位置 */
.sticky-bottom-form {
  position: sticky;
  bottom: 0;
  background: white;
  padding-top: 16px;
  z-index: 1;
}

.text-blue-500 {
  color: #3b82f6;
}
.hover\:cursor-pointer:hover {
  cursor: pointer;
  text-decoration: underline;
}

.supplier-item {
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.n-form-item-label {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.n-form-item-label__required::before {
  color: #ff4d4f;
  margin-right: 4px;
}
</style>
