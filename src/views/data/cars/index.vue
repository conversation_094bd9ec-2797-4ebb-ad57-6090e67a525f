<script setup>
import { onMounted, ref, h } from 'vue'
import {
  NSelect,
  NButton,
  NDataTable,
  NPagination,
  NGrid,
  NGi,
  NFormItem,
  NModal,
  NCard,
  NDescriptions,
  NDescriptionsItem,
  useMessage,
} from 'naive-ui'

import QuerySelect from '@/components/query-bar/QuerySelect.vue'
import CommonPage from '@/components/page/CommonPage.vue'
// import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
// import CrudTable from '@/components/table/CrudTable.vue'

import api from '@/api'

defineOptions({ name: '车型数据' })

const message = useMessage()

// const $table = ref(null)
// const queryItems = ref({})
const queryItems = ref({
  country: null,
  vehicle_type: null,
  brand: null,
  series: null,
  model_year: null,
  displacement: null,
  trans_type: null,
  fuel_type: null,
  start_time: null,
  end_time: null,
})
const tabOptions = ref(['全部', '中国', '北美', '欧洲', '俄罗斯', '加拿大', '墨西哥', '东南亚'])
const tableData = ref([])
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
})

async function fetchData() {
  try {
    const params = {
      ...queryItems.value,
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
    }
    const res = await api.getCarsList(params)
    tableData.value = res.data
    pagination.value.itemCount = res.total
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

function handlePageChange(page) {
  pagination.value.page = page
  fetchData()
}

function handlePageSizeChange(pageSize) {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  fetchData()
}

async function handleSearch() {
  pagination.value.page = 1
  await fetchData()
}

async function customReset() {
  datetimeRange.value = null
  // 清空 queryItems 的内容
  queryItems.value = {
    country: null,
    vehicle_type: null,
    brand: null,
    series: null,
    model_year: null,
    displacement: null,
    trans_type: null,
    fuel_type: null,
    start_time: null,
    end_time: null,
  }
  // 重置 QuerySelect 组件状态
  if (querySelectRef.value) {
    querySelectRef.value.selectTab(tabOptions.value[0]) // 选中"全部"
  }
  // 重新填充所有下拉框的数据
  await fetchInitOptions()
  await handleSearch()
}

onMounted(() => {
  fetchInitOptions()
  handleSearch()
})

// 禁用未来的年份
const disabledDate = (ts) => {
  // 获取当前年份（使用本地时间）
  const currentYear = new Date().getFullYear()
  // 获取选择年份
  const selectedYear = new Date(ts).getFullYear()
  // 禁用未来年份
  return selectedYear > currentYear
}

const datetimeRange = ref(null)
// 日期范围处理（优化版）
const handleDateRangeChange = (value) => {
  if (!value) {
    queryItems.value.start_time = null
    queryItems.value.end_time = null
    return
  }

  let [startTs, endTs] = value
  const startYear = new Date(startTs).getFullYear()
  const endYear = new Date(endTs).getFullYear()

  // 自动修正时间顺序
  if (startYear > endYear) {
    ;[startTs, endTs] = [endTs, startTs] // 交换时间戳
    datetimeRange.value = [startTs, endTs] // 更新组件显示值
  }

  // 转换为字符串格式
  queryItems.value.start_time = `${Math.min(startYear, endYear)}`
  queryItems.value.end_time = `${Math.max(startYear, endYear)}`
}

// 控制弹窗的状态和数据
const showModal = ref(false)
const modalData = ref({})
const querySelectRef = ref(null)
const oedata = ref([])

const Detail = async (rowData) => {
  modalData.value = rowData // 将当前行数据存储到 modalData 中
  try {
    const oe_response = await api.getOeDataList({ reach_car_id: rowData.reach_car_id })
    oedata.value = [...(oe_response.data || [])]
  } catch (error) {
    oedata.value = []
  }
  showModal.value = true // 打开弹窗
}

const vehicle_type_options = ref([])
const brand_options = ref([])
const series_options = ref([])
const year_options = ref([])
const displacement_options = ref([])
const trans_options = ref([])
const fuel_type_options = ref([])

// 获取初始选项
const loading = ref(false)
const fetchInitOptions = async () => {
  loading.value = true
  try {
    const api_options = await api.getOptionList()
    vehicle_type_options.value = api_options.data.vehicle_type || []
    brand_options.value = api_options.data.brand || []
    series_options.value = api_options.data.series || []
    year_options.value = api_options.data.model_year || []
    displacement_options.value = api_options.data.displacement || []
    trans_options.value = api_options.data.trans_type || []
    fuel_type_options.value = api_options.data.fuel_type || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    loading.value = false // 完成加载
  }
}

const handleOptionChange = async (optionKey, selectedValue) => {
  loading.value = true
  try {
    if (optionKey == 'yearrange') {
      handleDateRangeChange(selectedValue)
    } else {
      queryItems.value[optionKey] = selectedValue
    }
    // 从后端获取并更新其它选项
    const updatedOptions = await api.getOptionList(queryItems.value)
    // 更新选项
    vehicle_type_options.value = updatedOptions.data.vehicle_type || []
    brand_options.value = updatedOptions.data.brand || []
    series_options.value = updatedOptions.data.series || []
    year_options.value = updatedOptions.data.model_year || []
    displacement_options.value = updatedOptions.data.displacement || []
    trans_options.value = updatedOptions.data.trans_type || []
    fuel_type_options.value = updatedOptions.data.fuel_type || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    loading.value = false
  }
}

const handleCountryChange = (value) => {
  queryItems.value.country = value === '全部' ? null : value
  handleOptionChange('country', queryItems.value.country)
  fetchData()
}

onMounted(() => {
  fetchInitOptions()
})

const oecolumns = [
  {
    title: 'Reach号/主号',
    key: 'group_number',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: 'OE/主号',
    key: 'PART_NUMBER',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '英文名称',
    key: 'PART_TERMINOLOGY_NAME',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '标准名称',
    key: 'standard_label',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
]

const columns = [
  {
    title: '品牌',
    key: 'brand',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '厂家',
    key: 'manufacturer',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型分类',
    key: 'vehicle_type',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车系',
    key: 'series',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型',
    key: 'model',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '年款',
    key: 'model_year',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '排量',
    key: 'displacement',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: 'Vio',
    key: 'vio',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '更多',
    key: 'actions',
    align: 'center',
    width: 'auto',
    render(row) {
      return h(
        NButton,
        {
          size: 'small',
          onClick: () => Detail(row),
        },
        { default: () => 'Detail' }
      )
    },
  },
]

// 在setup() 中添加滚动方法
const scrollTo = (id) => {
  const container = document.querySelector('.n-card__content [style*="overflow-y"]')
  const target = document.getElementById(id)

  if (container && target) {
    // 计算容器内相对位置
    const targetTop = target.offsetTop - container.offsetTop - 20
    container.scrollTo({
      top: targetTop,
      behavior: 'smooth',
    })
  }
}

// 新增 OE 详情相关状态
const showOeModal = ref(false)
const currentStandardLabel = ref('')
const productDetailData = ref({})

// 标准名称与字段映射配置
const standardLabelConfigs = {
  水箱: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '芯体长（英寸）', key: '芯体长（英寸）' },
    { label: '芯体宽（英寸）', key: '芯体宽（英寸）' },
    { label: '芯体厚（英寸）', key: '芯体厚（英寸）' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '水室方向', key: '水室方向' },
    { label: 'TOC规格', key: 'TOC规格' },
    { label: 'TOC油冷器中心距mm', key: 'TOC油冷器中心距mm' },
    { label: 'EOC规格', key: 'EOC规格' },
    { label: 'EOC油冷器中心距mm', key: 'EOC油冷器中心距mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: 'AT/MT', key: 'AT/MT' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '进口管直径mm(螺纹)', key: '进口管直径mm(螺纹)' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '出口管直径mm(螺纹)', key: '出口管直径mm(螺纹)' },
    { label: '芯体排数', key: '芯体排数' },
    { label: '是否带加水口', key: '是否带加水口' },
    { label: '是否带盖子', key: '是否带盖子' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  冷凝器: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '进口结构', key: '进口结构' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口结构', key: '出口结构' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '是否含干燥瓶', key: '是否含干燥瓶' },
    { label: '干燥瓶类型', key: '干燥瓶类型' },
    { label: '是否带干燥剂', key: '是否带干燥剂' },
    { label: '是否带储液器', key: '是否带储液器' },
    { label: '储液器类型', key: '储液器类型' },
    { label: '是否双系统油散', key: '是否双系统油散' },
    { label: '双系统油散进口尺寸', key: '双系统油散进口尺寸' },
    { label: '双系统油散出口尺寸', key: '双系统油散出口尺寸' },
    { label: '制冷剂', key: '制冷剂' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  暖风: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '是否带管子', key: '是否带管子' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  中冷器: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '进口管位置', key: '进口管位置' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口管位置', key: '出口管位置' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '冷却方式', key: '冷却方式' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  蒸发器: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '波高mm', key: '波高mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '材质', key: '材质' },
    { label: '结构', key: '结构' },
    { label: '进口结构', key: '进口结构' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '出口结构', key: '出口结构' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  压缩机: [
    { label: '电压【V】', key: '电压【V】' },
    { label: '插头结构', key: '插头结构' },
    { label: '皮带轮类型', key: '皮带轮类型' },
    { label: '沟槽的数量', key: '沟槽的数量' },
    { label: '皮带盘直径mm', key: '皮带盘直径mm' },
    { label: '特定生产商', key: '特定生产商' },
    { label: '压缩机-ID', key: '压缩机-ID' },
    { label: '预充 PAG 油', key: '预充 PAG 油' },
    { label: '压缩机机油', key: '压缩机机油' },
    { label: '机油加注量ml', key: '机油加注量ml' },
    { label: '排量-压缩机CC', key: '排量-压缩机CC' },
    { label: '制冷剂', key: '制冷剂' },
    { label: '固定方式', key: '固定方式' },
    { label: '固定孔的数量', key: '固定孔的数量' },
    { label: '离合器连接器性别', key: '离合器连接器性别' },
    { label: '连接器数量', key: '连接器数量' },
    { label: '终端数量', key: '终端数量' },
    { label: '包括离合器', key: '包括离合器' },
    { label: '包含交换机', key: '包含交换机' },
    { label: '交换机服务端口', key: '交换机服务端口' },
    { label: '线圈时钟位置', key: '线圈时钟位置' },
    { label: '线圈数量', key: '线圈数量' },
    { label: '旋转方向', key: '旋转方向' },
    { label: '是否含O型圈', key: '是否含O型圈' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
    { label: 'C-O型圈规格', key: 'C-O型圈规格' },
    { label: 'C-O型圈数量', key: 'C-O型圈数量' },
    { label: 'C-O型圈位置', key: 'C-O型圈位置' },
    { label: 'D-O型圈规格', key: 'D-O型圈规格' },
    { label: 'D-O型圈数量', key: 'D-O型圈数量' },
    { label: 'D-O型圈位置', key: 'D-O型圈位置' },
    { label: '安装孔上下间距mm', key: '安装孔上下间距mm' },
    { label: '安装孔左右间距mm', key: '安装孔左右间距mm' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  传感器: [
    { label: '电压【V】', key: '电压【V】' },
    { label: '插头结构', key: '插头结构' },
    { label: '插塞接头数量', key: '插塞接头数量' },
    { label: '长度mm', key: '长度mm' },
    { label: '材质', key: '材质' },
    { label: '颜色', key: '颜色' },
    { label: '传感器类型', key: '传感器类型' },
    { label: '安装位置', key: '安装位置' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  干燥瓶: [
    { label: '干燥瓶类型', key: '干燥瓶类型' },
    { label: '是否带干燥剂', key: '是否带干燥剂' },
    { label: '是否符合OEM', key: '是否符合OEM' },
    { label: '过滤材料', key: '过滤材料' },
    { label: '保险头的材质', key: '保险头的材质' },
    { label: '直径MM', key: '直径MM' },
    { label: '长度mm', key: '长度mm' },
    { label: '进口结构', key: '进口结构' },
    { label: '进口管直径mm(螺纹)', key: '进口管直径mm(螺纹)' },
    { label: '出口结构', key: '出口结构' },
    { label: '出口管直径mm(螺纹)', key: '出口管直径mm(螺纹)' },
    { label: '连接口尺寸', key: '连接口尺寸' },
    { label: '是否有保险', key: '是否有保险' },
    { label: '是否包含垫片', key: '是否包含垫片' },
    { label: '是否带支架', key: '是否带支架' },
    { label: '是否带管子', key: '是否带管子' },
    { label: '是否有安装硬件', key: '是否有安装硬件' },
    { label: '是否有减压阀', key: '是否有减压阀' },
    { label: '是否有安装衬垫', key: '是否有安装衬垫' },
    { label: '交换机服务端口', key: '交换机服务端口' },
    { label: '终端数量', key: '终端数量' },
    { label: '是否有螺丝/钉子', key: '是否有螺丝/钉子' },
    { label: '开关口尺寸MM', key: '开关口尺寸MM' },
    { label: '是否有开关', key: '是否有开关' },
    { label: '开关数量', key: '开关数量' },
    { label: '是否有观察的玻璃镜', key: '是否有观察的玻璃镜' },
    { label: '是否有玻璃镜', key: '是否有玻璃镜' },
    { label: '是否含O型圈', key: '是否含O型圈' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
    { label: 'C-O型圈规格', key: 'C-O型圈规格' },
    { label: 'C-O型圈数量', key: 'C-O型圈数量' },
    { label: 'C-O型圈位置', key: 'C-O型圈位置' },
    { label: 'D-O型圈规格', key: 'D-O型圈规格' },
    { label: 'D-O型圈数量', key: 'D-O型圈数量' },
    { label: 'D-O型圈位置', key: 'D-O型圈位置' },
    { label: '铝制插头规格', key: '铝制插头规格' },
    { label: '塑料插头规格', key: '塑料插头规格' },
    { label: '塑料网规格', key: '塑料网规格' },
    { label: '塑料头规格', key: '塑料头规格' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  膨胀阀: [
    { label: '芯体长mm', key: '芯体长mm' },
    { label: '芯体宽mm', key: '芯体宽mm' },
    { label: '芯体厚mm', key: '芯体厚mm' },
    { label: '材质', key: '材质' },
    { label: '压力(bar)', key: '压力(bar)' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '膨胀阀类型', key: '膨胀阀类型' },
    { label: '进口管直径mm', key: '进口管直径mm' },
    { label: '进口管直径(英寸)', key: '进口管直径(英寸)' },
    { label: '出口管直径mm', key: '出口管直径mm' },
    { label: '出口管直径(英寸)', key: '出口管直径(英寸)' },
    { label: '吸入口类型', key: '吸入口类型' },
    { label: '进口结构', key: '进口结构' },
    { label: '进气口直径mm', key: '进气口直径mm' },
    { label: '进气口直径(英寸)', key: '进气口直径(英寸)' },
    { label: '出口结构', key: '出口结构' },
    { label: '岀气口直径(mm)', key: '岀气口直径(mm)' },
    { label: '岀气口直径(英寸)', key: '岀气口直径(英寸)' },
    { label: '毛细管长度(mm)', key: '毛细管长度(mm)' },
    { label: '毛细管温包长度(mm)', key: '毛细管温包长度(mm)' },
    { label: '外平衡管毛细管长度(mm)', key: '外平衡管毛细管长度(mm)' },
    { label: '外平衡管尺寸(mm)', key: '外平衡管尺寸(mm)' },
    { label: '外平衡管尺寸(英寸)', key: '外平衡管尺寸(英寸)' },
    { label: '是否含O型圈', key: '是否含O型圈' },
    { label: 'A-O型圈规格', key: 'A-O型圈规格' },
    { label: 'A-O型圈数量', key: 'A-O型圈数量' },
    { label: 'A-O型圈位置', key: 'A-O型圈位置' },
    { label: 'B-O型圈规格', key: 'B-O型圈规格' },
    { label: 'B-O型圈数量', key: 'B-O型圈数量' },
    { label: 'B-O型圈位置', key: 'B-O型圈位置' },
    { label: 'C-O型圈规格', key: 'C-O型圈规格' },
    { label: 'C-O型圈数量', key: 'C-O型圈数量' },
    { label: 'C-O型圈位置', key: 'C-O型圈位置' },
    { label: 'D-O型圈规格', key: 'D-O型圈规格' },
    { label: 'D-O型圈数量', key: 'D-O型圈数量' },
    { label: 'D-O型圈位置', key: 'D-O型圈位置' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  电子扇: [
    { label: '电压【V】', key: '电压【V】' },
    { label: '额定功率【W】', key: '额定功率【W】' },
    { label: '直径MM', key: '直径MM' },
    { label: '插头结构', key: '插头结构' },
    { label: '插塞接头数量', key: '插塞接头数量' },
    { label: '通风机叶片的数量', key: '通风机叶片的数量' },
    { label: '是否带控制模块', key: '是否带控制模块' },
    { label: '净重（KG)', key: '净重（KG)' },
    { label: '附件', key: '附件' },
    { label: '备注', key: '备注' },
  ],
  default: [],
}

// 处理 OE 表格行点击
const handleOeRowClick = (rowData) => {
  if (!rowData.group_number) {
    message.warning('Reach号为空, 无属性')
    return
  }
  fetchProductDetail(rowData)
}

const rowProps = (row) => {
  return {
    style: 'cursor: pointer;', // 可选：改变鼠标样式
    onClick: () => handleOeRowClick(row),
  }
}

// 获取 OE 详细信息
const fetchProductDetail = async (row) => {
  try {
    // 模拟 API 调用，实际应替换为真实接口
    const res = await api.getProductDetail({
      group_number: row.group_number,
    })

    productDetailData.value = res.data
    currentStandardLabel.value = row.standard_label
    showOeModal.value = true
  } catch (error) {
    message.error('获取OE详情失败')
  }
}

// 计算当前配置
const currentConfig = computed(() => {
  return standardLabelConfigs[currentStandardLabel.value] || standardLabelConfigs.default
})
</script>

<template>
  <!-- 业务页面 -->
  <CommonPage>
    <!-- 表格 -->
    <n-grid :cols="20" :x-gap="12" class="mb-4">
      <n-gi :span="4">
        <n-form-item label="车型分类" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.vehicle_type"
            :options="vehicle_type_options"
            placeholder="车型分类"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('vehicle_type', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="品牌" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.brand"
            :options="brand_options"
            placeholder="品牌"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('brand', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="车系" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.series"
            :options="series_options"
            placeholder="车系"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('series', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="年款" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.model_year"
            :options="year_options"
            placeholder="年款"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('model_year', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="排量" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.displacement"
            :options="displacement_options"
            placeholder="排量"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('displacement', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="变速器类型" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.trans_type"
            :options="trans_options"
            placeholder="变速器类型"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('trans_type', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="燃油类型" :label-width="60" label-placement="left">
          <n-select
            v-model:value="queryItems.fuel_type"
            :options="fuel_type_options"
            placeholder="燃油类型"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('fuel_type', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="4">
        <n-form-item label="起止年份" :label-width="60" label-placement="left">
          <n-date-picker
            v-model:value="datetimeRange"
            type="yearrange"
            clearable
            placeholder="选择年份范围"
            :disabled-date="disabledDate"
            @update:value="handleDateRangeChange"
          />
        </n-form-item>
      </n-gi>

      <n-gi :span="4">
        <n-button type="primary" @click="handleSearch">搜索</n-button>
        <n-button class="ml-2" @click="customReset">重置</n-button>
      </n-gi>
    </n-grid>

    <!-- <QuerySelect class="mb-4" :options="tabOptions" @selected_value="handleCountryChange" /> -->
    <QuerySelect
      ref="querySelectRef"
      class="mb-4"
      :options="tabOptions"
      @selected_value="handleCountryChange"
    />
    <br />
    <!-- 数据表格 -->
    <n-data-table :columns="columns" :data="tableData" :bordered="false" class="mb-4" />

    <!-- 分页 -->
    <n-pagination
      v-model:page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :item-count="pagination.itemCount"
      :page-sizes="pagination.pageSizes"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    />

    <NModal v-model:show="showModal" preset="dialog" :style="{ width: '900px' }" title="详情信息">
      <NCard
        content-style="padding: 16px; max-height: 70vh; display: flex; flex-direction: column;"
      >
        <!-- 顶部标题 -->
        <div style="text-align: center; margin-bottom: 20px; font-size: 18px; font-weight: bold">
          {{ modalData.brand }} {{ modalData.series }} {{ modalData.model }}
          {{ modalData.model_year }}
        </div>

        <!-- 导航选项卡 -->
        <n-tabs
          type="line"
          default-value="vehicle"
          size="medium"
          style="margin-bottom: 16px; position: sticky; top: 0; background: white; z-index: 1"
        >
          <n-tab name="vehicle" @click="scrollTo('vehicle-details')">车型详情</n-tab>
          <n-tab name="oe" @click="scrollTo('oe-details')">OE详情</n-tab>
          <!-- <n-tab name="product" @click="scrollTo('product-details')">产品详情</n-tab> -->
        </n-tabs>

        <!-- 内容容器 -->
        <div style="flex: 1; overflow-y: auto; padding: 0 8px">
          <!-- 车型详情 -->
          <n-card
            id="vehicle-details"
            title="车型详情"
            size="small"
            :bordered="false"
            style="margin-bottom: 16px"
          >
            <n-descriptions bordered label-placement="left" :column="2" size="small">
              <n-descriptions-item label="Reach" label-style="font-weight: bold;">
                {{ modalData.reach_car_id || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="上市年份" label-style="font-weight: bold;">
                {{ modalData.year_from || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="车型分类" label-style="font-weight: bold;">
                {{ modalData.vehicle_type || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="停产年份" label-style="font-weight: bold;">
                {{ modalData.year_till || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="厂家" label-style="font-weight: bold;">
                {{ modalData.manufacturer || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="发动机型号" label-style="font-weight: bold;">
                {{ modalData.engine_code || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="品牌" label-style="font-weight: bold;">
                {{ modalData.brand || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="排量" label-style="font-weight: bold;">
                {{ modalData.displacement || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="车系" label-style="font-weight: bold;">
                {{ modalData.series || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="变速器描述" label-style="font-weight: bold;">
                {{ modalData.trans_desc || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="车型" label-style="font-weight: bold;">
                {{ modalData.model || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="排放标准" label-style="font-weight: bold;">
                {{ modalData.emission_standard || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="驱动类型" label-style="font-weight: bold;">
                {{ modalData.driver_type || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="燃油类型" label-style="font-weight: bold;">
                {{ modalData.fuel_type || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="底盘号" label-style="font-weight: bold;">
                {{ modalData.chassis || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="VIO" label-style="font-weight: bold;">
                {{ modalData.vio || '0' }}
              </n-descriptions-item>
              <n-descriptions-item label="来源" label-style="font-weight: bold;">
                {{ modalData.source || '-' }}
              </n-descriptions-item>
            </n-descriptions>
          </n-card>

          <!-- OE详情 -->
          <n-card
            id="oe-details"
            title="OE详情"
            size="small"
            :bordered="false"
            style="margin-bottom: 16px"
          >
            <n-data-table
              :columns="oecolumns"
              :data="oedata"
              :max-height="250"
              virtual-scroll
              :row-props="rowProps"
            />
          </n-card>
        </div>
      </NCard>
    </NModal>
    <n-modal v-model:show="showOeModal" :mask-closable="false">
      <n-card
        style="width: 1000px"
        title="OE 属性详情"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-descriptions bordered :column="3" label-placement="left">
          <n-descriptions-item label="标准名称" label-style="font-weight: bold;">
            {{ currentStandardLabel }}
          </n-descriptions-item>

          <template v-for="item in currentConfig" :key="item.key">
            <n-descriptions-item :label="item.label" label-style="font-weight: bold;">
              {{ productDetailData[item.key] || '-' }}
            </n-descriptions-item>
          </template>
        </n-descriptions>

        <template #footer>
          <n-button type="primary" @click="showOeModal = false">关闭</n-button>
        </template>
      </n-card>
    </n-modal>
  </CommonPage>
</template>

<style scoped>
/* 优化滚动容器样式 */
.n-card__content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 导航栏吸附顶部的样式 */
.n-tabs {
  box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.08);
}

.n-data-table-tr--hover {
  background-color: #f5f5f5;
  transition: background-color 0.3s;
}
</style>
