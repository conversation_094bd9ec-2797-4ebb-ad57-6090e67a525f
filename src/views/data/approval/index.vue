<template>
  <CommonPage>
    <div class="approval-page">
      <!-- 搜索区域 -->
      <n-card class="search-card" :bordered="false">
        <n-form inline :label-width="80">
          <n-form-item label="物料代码">
            <n-input
              v-model:value="searchForm.partCode"
              placeholder="物料代码"
              clearable
              style="width: 200px"
            />
          </n-form-item>
          <n-form-item label="零件名称">
            <n-input
              v-model:value="searchForm.partName"
              placeholder="零件名称"
              clearable
              style="width: 200px"
            />
          </n-form-item>
          <n-form-item label="规格型号">
            <n-input
              v-model:value="searchForm.specification"
              placeholder="规格型号"
              clearable
              style="width: 200px"
            />
          </n-form-item>
          <n-form-item label="审核状态">
            <n-select
              v-model:value="searchForm.approvalStatus"
              placeholder="审核状态"
              clearable
              style="width: 150px"
              :options="statusOptions"
            />
          </n-form-item>
          <n-form-item label="提交时间">
            <n-date-picker
              v-model:value="searchForm.dateRange"
              type="daterange"
              clearable
              style="width: 240px"
            />
          </n-form-item>
          <n-form-item>
            <n-space>
              <n-button type="primary" @click="handleSearch" :loading="loading">
                <template #icon>
                  <n-icon><SearchOutlined /></n-icon>
                </template>
                搜索
              </n-button>
              <n-button @click="handleReset">重置</n-button>
            </n-space>
          </n-form-item>
        </n-form>
      </n-card>
  
      <!-- 操作区域 -->
      <n-card class="action-card" :bordered="false">
        <n-space justify="space-between">
          <n-space>
            <n-button
              type="success"
              :disabled="!hasSelection"
              @click="handleBatchApproval('approved')"
              :loading="batchLoading"
            >
              <template #icon>
                <n-icon><CheckCircleOutlined /></n-icon>
              </template>
              批量通过 ({{ selectedRowKeys.length }})
            </n-button>
            <n-button
              type="error"
              :disabled="!hasSelection"
              @click="handleBatchApproval('rejected')"
              :loading="batchLoading"
            >
              <template #icon>
                <n-icon><CloseCircleOutlined /></n-icon>
              </template>
              批量拒绝 ({{ selectedRowKeys.length }})
            </n-button>
            <n-button @click="exportSelected" :disabled="!hasSelection">
              <template #icon>
                <n-icon><DownloadOutlined /></n-icon>
              </template>
              导出选中
            </n-button>
          </n-space>
          <n-space>
            <n-button @click="showColumnSettings = true">
              <template #icon>
                <n-icon><SettingOutlined /></n-icon>
              </template>
              列设置
            </n-button>
            <n-button @click="handleRefresh" :loading="loading">
              <template #icon>
                <n-icon><ReloadOutlined /></n-icon>
              </template>
              刷新
            </n-button>
          </n-space>
        </n-space>
      </n-card>
  
      <!-- 数据表格 -->
      <n-card :bordered="false">
        <n-data-table
          ref="dataTableRef"
          :columns="displayColumns"
          :data="tableData"
          :loading="loading"
          :pagination="pagination"
          :row-key="rowKey"
          v-model:checked-row-keys="selectedRowKeys"
          :scroll-x="tableScrollX"
          :max-height="600"
          :bordered="true"
          :single-line="false"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </n-card>
  
      <!-- 列设置弹窗 -->
      <n-modal
        v-model:show="showColumnSettings"
        preset="card"
        title="自定义显示列"
        style="width: 600px"
        :mask-closable="false"
      >
        <n-transfer
          ref="transferRef"
          v-model:value="selectedColumns"
          :options="allColumnOptions"
          source-title="可选列"
          target-title="显示列"
          :filterable="true"
          virtual-scroll
        />
        <template #footer>
          <n-space justify="end">
            <n-button @click="showColumnSettings = false">取消</n-button>
            <n-button @click="resetColumns">重置</n-button>
            <n-button type="primary" @click="saveColumnSettings">确定</n-button>
          </n-space>
        </template>
      </n-modal>
  
      <!-- 审批备注弹窗 -->
      <n-modal
        v-model:show="showApprovalModal"
        preset="card"
        :title="approvalModalTitle"
        style="width: 500px"
        :mask-closable="false"
      >
        <n-form>
          <n-form-item label="备注">
            <n-input
              v-model:value="approvalRemark"
              type="textarea"
              placeholder="请输入审批备注（可选）"
              :autosize="{ minRows: 3, maxRows: 6 }"
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button @click="showApprovalModal = false">取消</n-button>
            <n-button
              :type="currentApprovalAction === 'approved' ? 'success' : 'error'"
              @click="confirmApproval"
              :loading="approvalLoading"
            >
              确定{{ currentApprovalAction === 'approved' ? '通过' : '拒绝' }}
            </n-button>
          </n-space>
        </template>
      </n-modal>
    </div>
  </CommonPage>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted, h } from 'vue';
  import {
    NCard, NForm, NFormItem, NInput, NSelect, NDatePicker, NButton, NSpace,
    NDataTable, NModal, NTransfer, NIcon, NTag, NPopconfirm, NTooltip,
    useMessage, type DataTableColumns, type SelectOption
  } from 'naive-ui';
  import {
    SearchOutlined, CheckCircleOutlined, CloseCircleOutlined,
    DownloadOutlined, SettingOutlined, ReloadOutlined,
    EyeOutlined, EditOutlined
  } from '@vicons/antd';
  import CommonPage from '@/components/page/CommonPage.vue'
  import * as XLSX from 'xlsx';
  import api from '@/api';

  defineOptions({ name: '产品数据审批' })
  
  const message = useMessage();
  
  // 响应式数据
  const loading = ref(false);
  const batchLoading = ref(false);
  const approvalLoading = ref(false);
  const tableData = ref([]);
  const selectedRowKeys = ref<string[]>([]);
  const showColumnSettings = ref(false);
  const showApprovalModal = ref(false);
  const approvalRemark = ref('');
  const currentApprovalAction = ref<'approved' | 'rejected'>('approved');
  const currentApprovalItems = ref<any[]>([]);
  
  // 搜索表单
  const searchForm = ref({
    partCode: '',
    partName: '',
    specification: '',
    approvalStatus: null,
    dateRange: null
  });
  
  // 状态选项
  const statusOptions: SelectOption[] = [
    { label: '待审核', value: 'pending' },
    { label: '已通过', value: 'approved' },
    { label: '已拒绝', value: 'rejected' }
  ];
  
  // 分页配置
  const pagination = ref({
    page: 1,
    pageSize: 20,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100],
    showQuickJumper: true,
    prefix: ({ itemCount }) => `共 ${itemCount} 条记录`
  });
  
  // 表格行键
  const rowKey = (row: any) => row.id;
  
  // 计算属性
  const hasSelection = computed(() => selectedRowKeys.value.length > 0);
  const approvalModalTitle = computed(() => 
    currentApprovalAction.value === 'approved' ? '批准通过' : '拒绝申请'
  );
  
  // 所有可用列配置
  const allColumns: DataTableColumns<any> = [
    {
      type: 'selection',
      fixed: 'left'
    },
    {
      title: '物料代码',
      key: 'MaterialCode',
      fixed: 'left',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '前缀',
      key: 'prefix',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '尾缀',
      key: 'suffix',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '中文名称',
      key: 'ChineseName',
      width: 150,
      ellipsis: { tooltip: true }
    },
    {
      title: '规格型号',
      key: 'SpecModel',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '属性',
      key: 'Attributes',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '品牌',
      key: 'Brand',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '可供状态',
      key: 'Availability',
      width: 100,
      render: (row) => {
        const statusMap = {
          '可供': { label: '可供', type: 'success' },
          '停供': { label: '停供', type: 'error' },
          '开发中': { label: '开发中', type: 'warning' },
          '不可供': { label: '不可供', type: 'warning' },
          '外购首批': { label: '外购首批', type: 'success' }
        };
        const status = statusMap[row.Availability] || { label: '未知', type: 'default' };
        return h(NTag, { type: status.type as any }, { default: () => status.label });
      }
    },
    {
      title: '供应商',
      key: 'SupplierName',
      width: 150,
      ellipsis: { tooltip: true }
    },
    {
      title: '供应商编码',
      key: 'SupplierCode',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '含税单价',
      key: 'TaxPrice',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '适用市场',
      key: 'Market',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '英文车系',
      key: 'SeriesEN',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '英文车型',
      key: 'ModelEN',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '中文车系',
      key: 'SeriesCN',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '中文车型',
      key: 'ModelCN',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '长',
      key: 'Length',
      width: 80,
      ellipsis: { tooltip: true }
    },
    {
      title: '宽',
      key: 'Width',
      width: 80,
      ellipsis: { tooltip: true }
    },
    {
      title: '高',
      key: 'Height',
      width: 80,
      ellipsis: { tooltip: true }
    },
    {
      title: '包装类型',
      key: 'PackageType',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '单个净重',
      key: 'NetWeight',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '单个毛重',
      key: 'GrossWeight',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '内盒长【mm】',
      key: 'InnerBoxLength',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '内盒宽【mm】',
      key: 'InnerBoxWidth',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '内盒高【mm】',
      key: 'InnerBoxHeight',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '外箱长【mm】',
      key: 'OutBoxLength',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '外箱宽【mm】',
      key: 'OutBoxWidth',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '外箱高【mm】',
      key: 'OutBoxHeight',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '最小包装数量',
      key: 'MinPackagingNum',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '最小订货数量',
      key: 'MinOrderNum',
      width: 120,
      ellipsis: { tooltip: true }
    },
    {
      title: '审核状态',
      key: 'approval_status',
      width: 100,
      render: (row) => {
        const statusMap = {
          'pending': { label: '待审核', type: 'warning' },
          'approved': { label: '已通过', type: 'success' },
          'rejected': { label: '已拒绝', type: 'error' }
        };
        const status = statusMap[row.approval_status] || { label: '未知', type: 'default' };
        return h(NTag, { type: status.type as any }, { default: () => status.label });
      }
    },
    {
      title: '备注',
      key: 'remark',
      width: 200,
      ellipsis: { tooltip: true }
    },
    {
      title: '验证结果',
      key: 'part_status',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '验证说明',
      key: 'part_verify_desc',
      width: 150,
      ellipsis: { tooltip: true }
    },
    {
      title: '创建人',
      key: 'create_user',
      width: 100,
      ellipsis: { tooltip: true }
    },
    {
      title: '创建时间',
      key: 'create_time',
      width: 150,
      ellipsis: { tooltip: true }
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 180,
      render: (row) => {
        return h(NSpace, { size: 'small' }, {
          default: () => [
            h(NTooltip, { trigger: 'hover' }, {
              trigger: () => h(NButton, {
                size: 'small',
                type: 'primary',
                ghost: true,
                onClick: () => viewDetail(row)
              }, {
                icon: () => h(NIcon, null, { default: () => h(EyeOutlined) })
              }),
              default: () => '查看详情'
            }),
            row.approval_status === 'pending' ? [
              h(NPopconfirm, {
                onPositiveClick: () => handleSingleApproval(row, 'approved')
              }, {
                trigger: () => h(NButton, {
                  size: 'small',
                  type: 'success',
                  ghost: true
                }, { default: () => '通过' }),
                default: () => '确定通过此项申请？'
              }),
              h(NButton, {
                size: 'small',
                type: 'error',
                ghost: true,
                onClick: () => handleSingleApproval(row, 'rejected')
              }, { default: () => '拒绝' })
            ] : null
          ].filter(Boolean)
        });
      }
    }
  ];
  
  // 列选择相关
  const defaultColumns = ['partCode', 'chineseName', 'specification', 'availableStatus', 
    'supplierName', 'approvalStatus', 'submitter', 'submitTime', 'actions'];
  
  const selectedColumns = ref<string[]>([...defaultColumns]);
  const STORAGE_KEY = 'approval-table-columns';
  
  // 从本地存储加载列配置
  const loadColumnSettings = () => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const savedColumns = JSON.parse(saved);
        // 确保加载的列配置是有效的
        const validColumns = savedColumns.filter((col: string) => 
          allColumnOptions.value.some(option => option.value === col)
        );
        selectedColumns.value = validColumns.length > 0 ? validColumns : [...defaultColumns];
      }
    } catch (error) {
      console.warn('Failed to load column settings:', error);
      selectedColumns.value = [...defaultColumns];
    }
  };
  
  // 保存列配置到本地存储
  const saveColumnSettings = () => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(selectedColumns.value));
      showColumnSettings.value = false;
      message.success('列设置已保存');
    } catch (error) {
      message.error('保存列设置失败');
    }
  };
  
  // 重置列配置
  const resetColumns = () => {
    selectedColumns.value = [...defaultColumns];
  };
  
  // 计算显示的列
  const displayColumns = computed(() => {
    return allColumns.filter(col => 
      col.type === 'selection' || 
      selectedColumns.value.includes(col.key as string) ||
      col.key === 'actions'
    );
  });
  
  // 表格滚动宽度
  const tableScrollX = computed(() => {
    return displayColumns.value.reduce((total, col) => {
      return total + (col.width || 120);
    }, 0);
  });
  
  // 列选择器选项
  const allColumnOptions = computed(() => {
    return allColumns
      .filter(col => col.key && col.key !== 'actions' && col.type !== 'selection')
      .map(col => ({
        label: col.title as string,
        value: col.key as string
      }));
  });
  
  // 渲染标签函数 - 移除这个函数，因为 NTransfer 会自动使用 option.label
  
  // API 调用函数
  const fetchTableData = async () => {
    loading.value = true;
    try {
      const params = {
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
        ...searchForm.value,
        startDate: searchForm.value.dateRange?.[0],
        endDate: searchForm.value.dateRange?.[1]
      };
      
      const response = await api.getPendingApprovals(params);
      
      tableData.value = response.data;
      pagination.value.itemCount = response.total;
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败，请重试');
    } finally {
      loading.value = false;
    }
  };
  
  // 事件处理函数
  const handleSearch = () => {
    pagination.value.page = 1;
    fetchTableData();
  };
  
  const handleReset = () => {
    searchForm.value = {
      partCode: '',
      partName: '',
      specification: '',
      approvalStatus: null,
      dateRange: null
    };
    handleSearch();
  };
  
  const handleRefresh = () => {
    fetchTableData();
  };
  
  const handlePageChange = (page: number) => {
    pagination.value.page = page;
    fetchTableData();
  };
  
  const handlePageSizeChange = (pageSize: number) => {
    pagination.value.pageSize = pageSize;
    pagination.value.page = 1;
    fetchTableData();
  };
  
  // 单个审批
  const handleSingleApproval = (row: any, action: 'approved' | 'rejected') => {
    currentApprovalItems.value = [row];
    currentApprovalAction.value = action;
    
    if (action === 'rejected') {
      approvalRemark.value = '';
      showApprovalModal.value = true;
    } else {
      confirmApproval();
    }
  };
  
  // 批量审批
  const handleBatchApproval = (action: 'approved' | 'rejected') => {
    const selectedItems = tableData.value.filter(item => 
      selectedRowKeys.value.includes(item.id)
    );
    
    if (selectedItems.length === 0) {
      message.warning('请选择要审批的数据');
      return;
    }
    
    currentApprovalItems.value = selectedItems;
    currentApprovalAction.value = action;
    
    if (action === 'rejected') {
      approvalRemark.value = '';
      showApprovalModal.value = true;
    } else {
      confirmApproval();
    }
  };
  
  // 确认审批
  const confirmApproval = async () => {
    approvalLoading.value = true;
    batchLoading.value = true;
    
    try {
      const ids = currentApprovalItems.value.map(item => item.id);
      
      await api.approveItems({
        ids,
        action: currentApprovalAction.value,
        remark: approvalRemark.value
      });
      
      message.success(`${currentApprovalAction.value === 'approved' ? '通过' : '拒绝'}操作完成`);
      
      showApprovalModal.value = false;
      selectedRowKeys.value = [];
      await fetchTableData();
    } catch (error) {
      console.error('审批操作失败:', error);
      message.error('审批操作失败，请重试');
    } finally {
      approvalLoading.value = false;
      batchLoading.value = false;
    }
  };
  
  // 查看详情
  const viewDetail = (row: any) => {
    // 这里可以打开详情弹窗或跳转到详情页面
    console.log('查看详情:', row);
    message.info('查看详情功能待实现');
  };
  
  // 导出选中数据
  const exportSelected = () => {
    const selectedData = tableData.value.filter(item => 
      selectedRowKeys.value.includes(item.id)
    );
    
    if (selectedData.length === 0) {
      message.warning('请选择要导出的数据');
      return;
    }
    
    try {
      // 准备导出数据
      const exportData = selectedData.map(row => {
        const result: any = {};
        displayColumns.value.forEach(col => {
          if (col.key && col.key !== 'actions' && col.type !== 'selection') {
            result[col.title as string] = row[col.key];
          }
        });
        return result;
      });
      
      // 创建Excel文件
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '审批数据');
      
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `产品审批数据_${timestamp}.xlsx`;
      
      XLSX.writeFile(workbook, filename);
      message.success('数据导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    }
  };
  
  // 组件挂载时的操作
  onMounted(() => {
    loadColumnSettings();
    fetchTableData();
  });
  </script>
  
  <style scoped>
  .approval-page {
    padding: 16px;
  }
  
  .search-card,
  .action-card {
    margin-bottom: 16px;
  }
  
  .search-card .n-form {
    margin-bottom: 0;
  }
  
  .action-card .n-space {
    flex-wrap: wrap;
  }
  
  :deep(.n-data-table-th) {
    font-weight: 600;
  }
  
  :deep(.n-data-table-td) {
    vertical-align: middle;
  }
  
  :deep(.n-transfer) {
    height: 400px;
  }
  </style>
  