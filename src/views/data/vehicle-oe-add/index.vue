<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NButton,
  NCard,
  NGrid,
  NGi,
  NRadioGroup,
  NRadio,
  NDatePicker,
  NSpace,
  NIcon,
  NDescriptions,
  NDescriptionsItem,
  useMessage,
  NDivider,
  NTag,
} from 'naive-ui'
import { AddCircleOutline, RemoveCircleOutline, SaveOutline } from '@vicons/ionicons5'
import CommonPage from '@/components/page/CommonPage.vue'
import api from '@/api'

defineOptions({ name: '新增车型或OE信息' })

const message = useMessage()

// 表单引用
const formRef = ref(null)

// 当前用户信息
const currentUser = ref({
  username: '',
  department: '',
  email: '',
})

// 任务信息
const taskInfo = ref({
  source: '系统录入',
  initiator: '',
  initiateTime: new Date(),
  taskType: 'vehicle', // vehicle: 车型, oe: OE信息
})

// 表单数据
const formData = ref({
  // 基础信息
  taskType: 'vehicle',
  vehicleType: null,
  brand: null,
  series: '',
  model: '',
  modelYear: null,
  displacement: null,
  transType: null,
  fuelType: null,

  // OE信息
  oeNumbers: [{ oeNumber: '', brandName: '', brandNumber: '' }],

  // 品牌信息选择
  brandInputType: 'select', // select: 下拉选择, input: 手动输入
  customBrandName: '',

  // 备注
  remarks: '',
})

// 下拉选项数据
const options = ref({
  vehicleTypes: [],
  brands: [],
  displacements: [],
  transTypes: [],
  fuelTypes: [],
  brandNames: [],
})

// 表单验证规则
const rules = {
  taskType: {
    required: true,
    message: '请选择任务类型',
    trigger: ['change', 'blur'],
  },
  vehicleType: {
    required: true,
    message: '请选择车型分类',
    trigger: ['change', 'blur'],
  },
  brand: {
    required: true,
    message: '请选择品牌',
    trigger: ['change', 'blur'],
  },
  series: {
    required: true,
    message: '请输入车系',
    trigger: ['input', 'blur'],
  },
  model: {
    required: true,
    message: '请输入车型',
    trigger: ['input', 'blur'],
  },
  modelYear: {
    required: true,
    message: '请选择年款',
    trigger: ['change', 'blur'],
  },
}

// 计算属性
const isVehicleType = computed(() => formData.value.taskType === 'vehicle')
const isOEType = computed(() => formData.value.taskType === 'oe')

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const userInfo = await api.getUserInfo()
    currentUser.value = {
      username: userInfo.username || '当前用户',
      department: userInfo.department || '技术部',
      email: userInfo.email || '<EMAIL>',
    }
    taskInfo.value.initiator = currentUser.value.username
  } catch (error) {
    console.error('获取用户信息失败:', error)
    // 使用默认值
    currentUser.value = {
      username: '当前用户',
      department: '技术部',
      email: '<EMAIL>',
    }
    taskInfo.value.initiator = currentUser.value.username
  }
}

// 获取下拉选项数据
const fetchOptions = async () => {
  try {
    const optionData = await api.getOptionList()
    options.value = {
      vehicleTypes: (optionData.vehicle_types || []).map((item) => ({
        label: item.label || item.name || item,
        value: item.value || item.code || item,
      })),
      brands: (optionData.brands || []).map((item) => ({
        label: item.label || item.name || item,
        value: item.value || item.code || item,
      })),
      displacements: (optionData.displacements || []).map((item) => ({
        label: item.label || item.name || item,
        value: item.value || item.code || item,
      })),
      transTypes: (optionData.trans_types || []).map((item) => ({
        label: item.label || item.name || item,
        value: item.value || item.code || item,
      })),
      fuelTypes: (optionData.fuel_types || []).map((item) => ({
        label: item.label || item.name || item,
        value: item.value || item.code || item,
      })),
      brandNames: (optionData.brand_names || []).map((item) => ({
        label: item.label || item.name || item,
        value: item.value || item.code || item,
      })),
    }
  } catch (error) {
    console.error('获取选项数据失败:', error)
    message.error('获取选项数据失败')
    // 提供默认选项
    options.value = {
      vehicleTypes: [
        { label: '轿车', value: 'sedan' },
        { label: 'SUV', value: 'suv' },
        { label: 'MPV', value: 'mpv' },
        { label: '跑车', value: 'sports' },
      ],
      brands: [
        { label: '奔驰', value: 'mercedes' },
        { label: '宝马', value: 'bmw' },
        { label: '奥迪', value: 'audi' },
        { label: '大众', value: 'volkswagen' },
      ],
      displacements: [
        { label: '1.0T', value: '1.0T' },
        { label: '1.5T', value: '1.5T' },
        { label: '2.0T', value: '2.0T' },
        { label: '3.0T', value: '3.0T' },
      ],
      transTypes: [
        { label: '手动', value: 'manual' },
        { label: '自动', value: 'automatic' },
        { label: 'CVT', value: 'cvt' },
      ],
      fuelTypes: [
        { label: '汽油', value: 'gasoline' },
        { label: '柴油', value: 'diesel' },
        { label: '混动', value: 'hybrid' },
        { label: '纯电', value: 'electric' },
      ],
      brandNames: [
        { label: 'Bosch', value: 'bosch' },
        { label: 'Continental', value: 'continental' },
        { label: 'Delphi', value: 'delphi' },
        { label: 'Denso', value: 'denso' },
      ],
    }
  }
}

// 添加OE号
const addOENumber = () => {
  formData.value.oeNumbers.push({ oeNumber: '', brandName: '', brandNumber: '' })
}

// 删除OE号
const removeOENumber = (index) => {
  if (formData.value.oeNumbers.length > 1) {
    formData.value.oeNumbers.splice(index, 1)
  }
}

// 处理品牌输入类型变化
const handleBrandInputTypeChange = (value) => {
  formData.value.brandInputType = value
  if (value === 'select') {
    formData.value.customBrandName = ''
  }
}

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    const submitData = {
      taskInfo: taskInfo.value,
      ...formData.value,
      submitTime: new Date(),
    }

    // 根据任务类型调用不同的API
    if (isVehicleType.value) {
      await api.createVehicle(submitData)
      message.success('车型信息创建成功')
    } else {
      await api.createOE(submitData)
      message.success('OE信息创建成功')
    }

    // 重置表单
    resetForm()
  } catch (error) {
    console.error('提交失败:', error)
    if (error?.message) {
      message.error(`提交失败: ${error.message}`)
    } else {
      message.error('提交失败，请检查表单信息')
    }
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    taskType: 'vehicle',
    vehicleType: null,
    brand: null,
    series: '',
    model: '',
    modelYear: null,
    displacement: null,
    transType: null,
    fuelType: null,
    oeNumbers: [{ oeNumber: '', brandName: '', brandNumber: '' }],
    brandInputType: 'select',
    customBrandName: '',
    remarks: '',
  }
  formRef.value?.restoreValidation()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUserInfo()
  fetchOptions()
})
</script>

<template>
  <CommonPage>
    <NCard title="新增车型或OE信息" class="mb-4">
      <!-- 任务信息展示 -->
      <NDescriptions :column="3" bordered class="mb-6">
        <NDescriptionsItem label="任务来源">
          <NTag type="info">{{ taskInfo.source }}</NTag>
        </NDescriptionsItem>
        <NDescriptionsItem label="任务发起人">
          <NTag type="success">{{ taskInfo.initiator }}</NTag>
        </NDescriptionsItem>
        <NDescriptionsItem label="发起时间">
          {{ new Date(taskInfo.initiateTime).toLocaleString() }}
        </NDescriptionsItem>
      </NDescriptions>

      <NDivider />

      <!-- 主表单 -->
      <NForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        :label-width="120"
        require-mark-placement="right-hanging"
      >
        <!-- 任务类型选择 -->
        <NFormItem label="任务类型" path="taskType">
          <NRadioGroup v-model:value="formData.taskType">
            <NSpace>
              <NRadio value="vehicle">新增车型</NRadio>
              <NRadio value="oe">新增OE信息</NRadio>
            </NSpace>
          </NRadioGroup>
        </NFormItem>

        <!-- 车型信息表单 -->
        <template v-if="isVehicleType">
          <NGrid :cols="2" :x-gap="24">
            <NGi>
              <NFormItem label="车型分类" path="vehicleType">
                <NSelect
                  v-model:value="formData.vehicleType"
                  :options="options.vehicleTypes"
                  placeholder="请选择车型分类"
                  filterable
                  clearable
                />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem label="品牌" path="brand">
                <NSelect
                  v-model:value="formData.brand"
                  :options="options.brands"
                  placeholder="请选择品牌"
                  filterable
                  clearable
                />
              </NFormItem>
            </NGi>
          </NGrid>

          <NGrid :cols="2" :x-gap="24">
            <NGi>
              <NFormItem label="车系" path="series">
                <NInput v-model:value="formData.series" placeholder="请输入车系" clearable />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem label="车型" path="model">
                <NInput v-model:value="formData.model" placeholder="请输入车型" clearable />
              </NFormItem>
            </NGi>
          </NGrid>

          <NGrid :cols="3" :x-gap="24">
            <NGi>
              <NFormItem label="年款" path="modelYear">
                <NDatePicker
                  v-model:value="formData.modelYear"
                  type="year"
                  placeholder="请选择年款"
                  clearable
                />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem label="排量">
                <NSelect
                  v-model:value="formData.displacement"
                  :options="options.displacements"
                  placeholder="请选择排量"
                  filterable
                  clearable
                />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem label="变速器类型">
                <NSelect
                  v-model:value="formData.transType"
                  :options="options.transTypes"
                  placeholder="请选择变速器类型"
                  filterable
                  clearable
                />
              </NFormItem>
            </NGi>
          </NGrid>

          <NFormItem label="燃油类型">
            <NSelect
              v-model:value="formData.fuelType"
              :options="options.fuelTypes"
              placeholder="请选择燃油类型"
              filterable
              clearable
              style="width: 300px"
            />
          </NFormItem>
        </template>

        <!-- OE信息表单 -->
        <template v-if="isOEType">
          <NFormItem label="OE号信息">
            <div class="w-full">
              <div
                v-for="(oe, index) in formData.oeNumbers"
                :key="index"
                class="mb-4 border border-gray-200 rounded p-4"
              >
                <NGrid :cols="4" :x-gap="12" class="mb-3">
                  <NGi>
                    <NInput v-model:value="oe.oeNumber" placeholder="请输入OE号" clearable />
                  </NGi>
                  <NGi>
                    <NRadioGroup
                      v-model:value="formData.brandInputType"
                      @update:value="handleBrandInputTypeChange"
                    >
                      <NSpace>
                        <NRadio value="select">选择品牌</NRadio>
                        <NRadio value="input">输入品牌</NRadio>
                      </NSpace>
                    </NRadioGroup>
                  </NGi>
                  <NGi>
                    <NSelect
                      v-if="formData.brandInputType === 'select'"
                      v-model:value="oe.brandName"
                      :options="options.brandNames"
                      placeholder="请选择品牌名称"
                      filterable
                      clearable
                    />
                    <NInput
                      v-else
                      v-model:value="oe.brandName"
                      placeholder="请输入品牌名称"
                      clearable
                    />
                  </NGi>
                  <NGi>
                    <NInput v-model:value="oe.brandNumber" placeholder="请输入品牌号" clearable />
                  </NGi>
                </NGrid>

                <NSpace justify="end">
                  <NButton
                    v-if="formData.oeNumbers.length > 1"
                    type="error"
                    size="small"
                    @click="removeOENumber(index)"
                  >
                    <template #icon>
                      <NIcon><RemoveCircleOutline /></NIcon>
                    </template>
                    删除
                  </NButton>
                </NSpace>
              </div>

              <NButton type="primary" dashed @click="addOENumber">
                <template #icon>
                  <NIcon><AddCircleOutline /></NIcon>
                </template>
                添加OE号
              </NButton>
            </div>
          </NFormItem>
        </template>

        <!-- 备注信息 -->
        <NFormItem label="备注信息">
          <NInput
            v-model:value="formData.remarks"
            type="textarea"
            placeholder="请输入备注信息（可选）"
            :autosize="{ minRows: 3, maxRows: 6 }"
            clearable
          />
        </NFormItem>

        <!-- 操作按钮 -->
        <NFormItem>
          <NSpace>
            <NButton type="primary" size="large" @click="handleSubmit">
              <template #icon>
                <NIcon><SaveOutline /></NIcon>
              </template>
              保存
            </NButton>
            <NButton size="large" @click="resetForm"> 重置 </NButton>
          </NSpace>
        </NFormItem>
      </NForm>
    </NCard>
  </CommonPage>
</template>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mb-3 {
  margin-bottom: 12px;
}

.w-full {
  width: 100%;
}

.p-4 {
  padding: 16px;
}

.border {
  border: 1px solid;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.rounded {
  border-radius: 6px;
}
</style>
