<template>
  <CommonPage show-footer title="源数据">
    <template #action>
      <div>
        <n-button type="primary" ghost @click="showImportDialog = true">
          <template #icon>
            <n-icon><UploadOutlined /></n-icon>
          </template>
          导入数据
        </n-button>
      </div>
    </template>
    <div class="container">
      <!-- 数据卡片展示 -->
      <n-grid x-gap="20" y-gap="20" :cols="4" responsive="screen" class="card-grid">
        <n-gi v-for="(item, index) in currentPageData" :key="index">
          <n-card hoverable @click="handleCardClick(item)">
            <n-skeleton v-if="loading" text :repeat="4" />
            <template v-else>
              <h3>{{ item.table_name }}</h3>
              <div class="meta-info">
                <n-text depth="3">字段数: {{ item.table_filed_count }}</n-text>
                <n-text depth="3">行数: {{ item.table_count }}</n-text>
                <n-text depth="3">大小: {{ formatSize(item.table_size) }}</n-text>
              </div>
            </template>
          </n-card>
        </n-gi>
      </n-grid>

      <!-- 分页 -->
      <n-pagination
        v-model:page="currentPage"
        :page-count="totalPages"
        @update:page="handlePageChange"
      />

      <!-- 数据详情弹窗 -->
      <n-modal v-model:show="showModal">
        <n-card style="width: 600px" title="数据详情" :bordered="false" size="huge">
          <template #header-extra>
            <n-button @click="refreshData">刷新</n-button>
          </template>

          <div v-for="(value, key) in currentRowData" :key="key" class="data-row">
            <n-text strong>{{ key }}: </n-text>
            <n-text>{{ value }}</n-text>
          </div>
        </n-card>
      </n-modal>
    </div>

    <!-- 在原有代码基础上新增历史记录对话框 -->
    <n-modal v-model:show="showHistoryDialog">
      <n-card style="width: 80%; max-width: 1000px" title="导入历史" size="huge">
        <n-data-table
          :columns="historyColumns"
          :data="importHistory"
          :pagination="historyPagination"
        />
      </n-card>
    </n-modal>

    <n-modal v-model:show="showImportDialog">
      <n-card style="width: 600px" title="数据导入" :bordered="false" size="huge">
        <template #header-extra>
          <!-- <n-button text @click="showHistoryDialog = true"> -->
          <n-button text @click="updateHistoryList">
            <n-icon><HistoryOutlined /></n-icon>
            查看历史
          </n-button>
        </template>
        <n-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-placement="left"
          label-width="auto"
        >
          <n-radio-group v-model:value="importType" name="importType" style="margin-bottom: 24px">
            <n-space>
              <n-radio value="new">新增表导入</n-radio>
              <n-radio value="existing">已有表更新</n-radio>
            </n-space>
          </n-radio-group>

          <!-- 新增表表单 -->
          <n-form-item v-if="importType === 'new'" label="表名称" path="tableName">
            <n-input v-model:value="form.tableName" placeholder="请输入新表名称" />
          </n-form-item>

          <!-- 已有表选择 -->
          <n-form-item v-if="importType === 'existing'" label="选择数据表" path="existingTable">
            <n-select
              v-model:value="form.existingTable"
              :options="tableOptions"
              placeholder="请选择要更新的数据表"
            />
          </n-form-item>
          <n-form-item v-if="importType === 'existing'" label="版本号输入" path="existingTable">
            <n-input v-model:value="form.tableVersion" placeholder="请输入版本号" />
          </n-form-item>

          <!-- 文件上传 -->
          <n-upload
            ref="uploadRef"
            action="#"
            :show-file-list="false"
            accept=".xlsx,.xls,.csv"
            :before-upload="beforeUpload"
            @change="handleFileChange"
          >
            <n-button type="primary" class="mb-4">
              <template #icon>
                <n-icon><FolderOpenOutlined /></n-icon>
              </template>
              选择文件
            </n-button>
          </n-upload>

          <div v-if="selectedFile" class="file-info">
            <n-space align="center">
              <n-icon color="#2080f0" :component="FileOutlined" />
              <div>
                <n-text strong>{{ selectedFile.name }}</n-text>
                <n-text depth="3" class="ml-2"> ({{ formatSize(selectedFile.size) }}) </n-text>
              </div>
              <n-button text type="error" @click="selectedFile = null"> 清除 </n-button>
            </n-space>
          </div>

          <n-space justify="end" style="margin-top: 24px">
            <n-button @click="showImportDialog = false">取消</n-button>
            <n-button type="primary" :loading="uploading" @click="handleSubmit">
              确认导入
            </n-button>
          </n-space>
        </n-form>
      </n-card>
    </n-modal>
  </CommonPage>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import CommonPage from '@/components/page/CommonPage.vue'
import { NTag, NSpin } from 'naive-ui'
import api from '@/api'
import { UploadOutlined, HistoryOutlined, FolderOpenOutlined, FileOutlined } from '@vicons/antd'

// 新增历史记录相关状态
const showHistoryDialog = ref(false)
const importHistory = ref([])

// 历史记录表格列配置
const historyColumns = [
  { title: '导入时间', key: 'created_at', width: 180 },
  { title: '文件名', key: 'file_name' },
  { title: '目标表', key: 'target_table' },
  {
    title: '状态',
    key: 'status',
    render(row) {
      return h('div', { class: 'status-cell' }, [
        row.status === '执行中'
          ? h(NSpin, {
              size: 'small',
              class: 'mr-2',
            })
          : null,
        h(
          NTag,
          {
            type: {
              导入成功: 'success',
              导入失败: 'error',
              执行中: 'warning',
            }[row.status],
            bordered: false,
            class: 'status-tag',
          },
          { default: () => row.status }
        ),
      ])
    },
  },
  { title: '文件大小', key: 'file_size' },
  { title: '导入用户', key: 'create_user' },
]

const updateHistoryList = async () => {
  showHistoryDialog.value = true
  try {
    const history_info_list = await api.getUploadHistory()
    importHistory.value = history_info_list.data
  } catch (error) {
    console.error('获取历史记录列表错误', error)
  }
}

// 分页配置
const historyPagination = {
  pageSize: 10,
}

// 新增文件选择状态
const selectedFile = ref(null)

// 处理文件选择变化
const handleFileChange = ({ file }) => {
  selectedFile.value = file.file
}

const getTableInfo = async () => {
  loading.value = true
  try {
    const table_info_list = await api.getTableInfoList()
    tableData.value = table_info_list.data
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    loading.value = false // 完成加载
  }
}

// 状态管理
const loading = ref(true)
const showModal = ref(false)
const currentPage = ref(1)
const pageSize = 12
const tableData = ref([])
const currentRowData = ref({})

// 计算属性
const totalPages = computed(() => Math.ceil(tableData.value.length / pageSize))
const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  return tableData.value.slice(start, start + pageSize)
})

// 卡片点击处理
const currentTableName = ref('')
const handleCardClick = async (item) => {
  // currentRowData.value = item.table_data || {}
  currentTableName.value = item.table_name
  await refreshData()
  showModal.value = true
}

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page
}

// 格式化文件大小
const formatSize = (bytes) => {
  const units = ['B', 'KB', 'MB', 'GB']
  let unitIndex = 0
  while (bytes >= 1024 && unitIndex < units.length - 1) {
    bytes /= 1024
    unitIndex++
  }
  return `${bytes.toFixed(2)} ${units[unitIndex]}`
}

// 导入对话框状态
const showImportDialog = ref(false)
const importType = ref('new')
const uploading = ref(false)
const formRef = ref(null)
const uploadRef = ref(null)

// 表单数据
const form = ref({
  tableName: '',
  existingTable: null,
})

// 验证规则
const rules = computed(() => ({
  tableName: {
    required: importType.value === 'new',
    trigger: ['blur', 'input'],
    message: '表名称不能为空',
  },
  existingTable: {
    required: importType.value === 'existing',
    trigger: ['blur', 'change'],
    message: '请选择要更新的数据表',
  },
}))

// 已有表选项（从已有数据获取）
const tableOptions = computed(() =>
  tableData.value.map((item) => ({
    label: item.table_name,
    value: item.table_name, // 假设数据表有唯一标识
  }))
)

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    await nextTick()
    // 检查文件是否选择
    if (!selectedFile.value) {
      window.$message.error('请选择要上传的文件')
      return
    }

    uploading.value = true

    // 构建表单数据
    const formData = new FormData()
    formData.append('file', selectedFile.value)

    // 根据类型添加参数
    if (importType.value === 'new') {
      formData.append('table_name', form.value.tableName)
    } else {
      formData.append('table_name', form.value.existingTable)
    }

    // 调用上传接口
    await api.uploadTableData(formData)

    window.$message.success('上传成功')
    showImportDialog.value = false
    // 刷新表格数据
    await getTableInfo()

    // 重置表单
    form.value = { tableName: '', existingTable: null }
    selectedFile.value = null
  } catch (error) {
    window.$message.error(error.message || '上传失败')
  } finally {
    uploading.value = false
  }
}

// 文件上传验证
const beforeUpload = async ({ file }) => {
  // 基础验证
  const validTypes = ['xlsx', 'xls', 'csv']
  const extension = file.name.split('.').pop().toLowerCase()

  if (!validTypes.includes(extension)) {
    window.$message.error('仅支持xlsx/xls/csv格式文件')
    return false
  }

  if (file.size > 5000 * 1024 * 1024) {
    window.$message.error('文件大小不能超过5000MB')
    return false
  }

  return true
}

// 模拟数据加载
onMounted(() => {
  getTableInfo()
})

// 刷新数据
const refreshData = async () => {
  try {
    // 调用API获取最新表数据
    const res = await api.getTableSampleData({ table_name: currentTableName.value })
    // 更新当前行数据和表格数据
    currentRowData.value = res.data

    window.$message.success('数据展示')
  } catch (error) {
    console.error('刷新失败', error)
    window.$message.error('数据展示失败')
  }
}
</script>

<style scoped>
.file-info {
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin: 16px 0;
}

.ml-2 {
  margin-left: 8px;
}

.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.upload-btn {
  margin-left: auto;
}

.card-grid {
  margin: 20px 0;
}

.meta-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.data-row {
  margin: 12px 0;
  padding: 8px;
  background: #f8f8f8;
  border-radius: 4px;
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-tag {
  &.n-tag--warning-type {
    background-color: #fffbe6;
    color: #faad14;
  }

  &.n-tag--success-type {
    background-color: #f6ffed;
    color: #52c41a;
  }

  &.n-tag--error-type {
    background-color: #fff2f0;
    color: #ff4d4f;
  }
}
</style>
