

const Layout = () => import('@/layout/index.vue')

export default {
  name: 'Workbench',
  path: '/workbench',
  component: Layout,
  redirect: '/workbench/tasks',
  meta: {
    title: '工作台',
    icon: 'icon-park-outline:workbench',
    order: 1,
  },
  children: [
    {
      name: 'WorkbenchDashboard',
      path: 'dashboard',
      component: () => import('./workbench/index.vue'),
      meta: {
        title: '工作台概览',
        icon: 'mdi:view-dashboard',
      },
    },
    {
      name: 'TaskList',
      path: 'tasks',
      component: () => import('./tasklist/index.vue'),
      meta: {
        title: '任务列表',
        icon: 'mdi:format-list-checks',
      },
    },
    {
      name: 'FlowEditor',
      path: 'flow',
      component: () => import('./flow/index.vue'),
      meta: {
        title: '流程编辑器',
        icon: 'mdi:flow-chart',
      },
    },
  ],
}
