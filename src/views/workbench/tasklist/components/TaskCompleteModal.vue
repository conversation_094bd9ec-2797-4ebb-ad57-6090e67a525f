<script setup>
import { ref, computed, watch } from 'vue'
import { NModal, NButton, NSpace, useMessage } from 'naive-ui'
import api from '@/api'
import VehicleOETaskForm from './VehicleOETaskForm.vue'

defineOptions({ name: 'TaskCompleteModal' })

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  task: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:visible', 'completed'])

const message = useMessage()
const loading = ref(false)

const show = computed({
  get() {
    return props.visible
  },
  set(value) {
    emit('update:visible', value)
  },
})

// 根据任务类型显示不同的表单组件
const currentTaskType = computed(() => props.task?.taskType || '')

// 任务类型映射
const taskTypeMap = {
  vehicle_oe: { title: '车型+OE任务完成', component: 'VehicleOETaskForm' },
  new_product: { title: '新增产品任务完成', component: null },
  inquiry_quote: { title: '询报价任务完成', component: null },
  new_product_dev: { title: '新产品开发任务完成', component: null },
}

const modalTitle = computed(() => {
  return taskTypeMap[currentTaskType.value]?.title || '任务完成'
})

// 表单引用
const formRef = ref(null)

// 处理任务完成
const handleComplete = async () => {
  try {
    loading.value = true

    // 如果有表单组件，先验证表单
    if (formRef.value && typeof formRef.value.validate === 'function') {
      const isValid = await formRef.value.validate()
      if (!isValid) {
        loading.value = false
        return
      }
    }

    // 获取表单数据
    let formData = {}
    if (formRef.value && typeof formRef.value.getFormData === 'function') {
      formData = formRef.value.getFormData()
    }

    // 调用完成任务的API
    const submitData = {
      taskId: props.task.id,
      taskType: props.task.taskType,
      ...formData,
      completedTime: new Date(),
    }

    try {
      // 调用实际的API接口
      await api.completeTask(submitData)
    } catch (apiError) {
      console.warn('API调用失败，使用模拟模式:', apiError)
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))
    }

    message.success('任务完成成功')
    emit('completed')
    show.value = false
  } catch (error) {
    console.error('完成任务失败:', error)
    message.error('完成任务失败，请重试')
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  show.value = false
}

// 监听任务变化，重置表单
watch(
  () => props.task,
  () => {
    if (formRef.value && typeof formRef.value.resetForm === 'function') {
      formRef.value.resetForm()
    }
  },
  { deep: true }
)
</script>

<template>
  <NModal
    v-model:show="show"
    :mask-closable="false"
    preset="card"
    :title="modalTitle"
    size="huge"
    style="width: 90%; max-width: 1200px"
  >
    <div class="task-complete-content">
      <!-- 任务基本信息 -->
      <div class="task-info mb-6">
        <h3 class="mb-3 text-lg font-medium">任务信息</h3>
        <div class="grid grid-cols-2 gap-4 rounded-lg bg-gray-50 p-4">
          <div>
            <span class="text-gray-600">任务名称：</span>
            <span class="font-medium">{{ task.taskName }}</span>
          </div>
          <div>
            <span class="text-gray-600">负责人：</span>
            <span class="font-medium">{{ task.assignee }}</span>
          </div>
          <div>
            <span class="text-gray-600">截止日期：</span>
            <span class="font-medium">{{ task.dueDate }}</span>
          </div>
          <div>
            <span class="text-gray-600">任务描述：</span>
            <span class="font-medium">{{ task.description }}</span>
          </div>
        </div>
      </div>

      <!-- 根据任务类型显示不同的表单 -->
      <div class="task-form">
        <!-- 车型+OE任务表单 -->
        <VehicleOETaskForm v-if="currentTaskType === 'vehicle_oe'" ref="formRef" :task="task" />

        <!-- 其他任务类型的占位符 -->
        <div v-else-if="currentTaskType === 'new_product'" class="py-8 text-center">
          <p class="text-gray-500">新增产品任务完成表单开发中...</p>
        </div>

        <div v-else-if="currentTaskType === 'inquiry_quote'" class="py-8 text-center">
          <p class="text-gray-500">询报价任务完成表单开发中...</p>
        </div>

        <div v-else-if="currentTaskType === 'new_product_dev'" class="py-8 text-center">
          <p class="text-gray-500">新产品开发任务完成表单开发中...</p>
        </div>

        <div v-else class="py-8 text-center">
          <p class="text-gray-500">未知任务类型</p>
        </div>
      </div>
    </div>

    <template #footer>
      <NSpace justify="end">
        <NButton @click="handleClose">取消</NButton>
        <NButton type="primary" :loading="loading" @click="handleComplete"> 完成任务 </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped>
.task-complete-content {
  max-height: 70vh;
  overflow-y: auto;
}

.mb-6 {
  margin-bottom: 24px;
}

.mb-3 {
  margin-bottom: 12px;
}

.text-lg {
  font-size: 18px;
}

.font-medium {
  font-weight: 500;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.gap-4 {
  gap: 16px;
}

.p-4 {
  padding: 16px;
}

.py-8 {
  padding-top: 32px;
  padding-bottom: 32px;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.rounded-lg {
  border-radius: 8px;
}

.text-center {
  text-align: center;
}

.text-gray-600 {
  color: #6b7280;
}

.text-gray-500 {
  color: #9ca3af;
}
</style>
