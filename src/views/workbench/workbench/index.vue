<template>
  <CommonPage>
    <div class="workbench-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <p class="page-subtitle">欢迎回来，{{ userInfo.name }}！创建和管理您的任务</p>
      </div>

      <!-- 快速创建任务 -->
      <div class="quick-tasks">
        <div
          v-for="task in quickTasks"
          :key="task.id"
          class="task-card"
          :class="task.colorClass"
          @click="openTaskModal(task.type)"
        >
          <div class="task-icon" :class="task.iconBgClass">
            <n-icon :component="task.icon" size="20" />
          </div>
          <h3 class="task-title">{{ task.title }}</h3>
          <p class="task-description">{{ task.description }}</p>
          <div class="task-action" :class="task.textColorClass">
            <span>创建任务</span>
            <n-icon :component="ArrowRight" size="14" />
          </div>
        </div>
      </div>

      <!-- 任务统计 -->
      <div class="stats-grid">
        <div v-for="stat in taskStats" :key="stat.label" class="stat-card">
          <div class="stat-content">
            <div class="stat-text">
              <p class="stat-label">{{ stat.label }}</p>
              <h3 class="stat-value" :class="{ 'text-danger': stat.isDanger }">{{ stat.value }}</h3>
            </div>
            <div class="stat-icon" :class="stat.iconBgClass">
              <n-icon :component="stat.icon" size="20" :color="stat.iconColor" />
            </div>
          </div>
          <p class="stat-trend" :class="stat.trendClass">
            <n-icon :component="stat.trendIcon" size="12" />
            {{ stat.trend }} <span class="trend-text">{{ stat.trendText }}</span>
          </p>
        </div>
      </div>

      <!-- 图表和任务进度 -->
      <div class="charts-section">
        <!-- 任务类型分布图表 -->
        <n-card class="chart-card" :bordered="false">
          <template #header>
            <div class="chart-header">
              <h3 class="chart-title">任务类型分布</h3>
              <div class="chart-tabs">
                <n-button
                  v-for="tab in chartTabs"
                  :key="tab.key"
                  :type="activeChartTab === tab.key ? 'primary' : 'default'"
                  size="small"
                  @click="activeChartTab = tab.key"
                >
                  {{ tab.label }}
                </n-button>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <VueEcharts :option="chartOption" style="height: 320px" />
          </div>
        </n-card>

        <!-- 任务进度 -->
        <n-card class="progress-card" :bordered="false">
          <template #header>
            <h3 class="progress-title">任务完成进度</h3>
          </template>
          <div class="progress-list">
            <div v-for="progress in taskProgress" :key="progress.name" class="progress-item">
              <div class="progress-header">
                <span class="progress-name">{{ progress.name }}</span>
                <span class="progress-percent">{{ progress.percent }}%</span>
              </div>
              <n-progress
                :percentage="progress.percent"
                :color="progress.color"
                :rail-color="'#f0f0f0'"
                :height="10"
                :border-radius="5"
              />
            </div>
          </div>

          <div class="overall-progress">
            <h4 class="overall-title">任务完成率</h4>
            <div class="overall-item">
              <div class="overall-header">
                <n-tag type="success" size="small">总体完成率</n-tag>
                <span class="overall-percent">65.5%</span>
              </div>
              <n-progress
                :percentage="65.5"
                color="#52c41a"
                :rail-color="'rgba(82, 196, 26, 0.2)'"
                :height="8"
                :border-radius="4"
              />
            </div>
          </div>
        </n-card>
      </div>

      <!-- 最近任务列表 -->
      <n-card class="task-table-card" :bordered="false">
        <template #header>
          <div class="table-header">
            <h3 class="table-title">最近任务</h3>
            <n-button text type="primary" size="small">查看全部</n-button>
          </div>
        </template>
        <n-data-table
          :columns="taskColumns"
          :data="recentTasks"
          :pagination="tablePagination"
          :bordered="false"
          size="medium"
        />
      </n-card>

      <!-- 任务创建模态框 -->
      <n-modal v-model:show="showTaskModal" preset="card" style="width: 600px" :title="modalTitle">
        <div class="task-form">
          <!-- 动态表单内容将根据任务类型生成 -->
          <div v-if="currentTaskType === '车型+OE数据'" class="form-content">
            <n-form :model="taskForm" label-placement="top">
              <n-form-item label="任务名称">
                <n-input v-model:value="taskForm.name" placeholder="例如：2023款车型数据录入" />
              </n-form-item>
              <n-form-item label="车型品牌">
                <n-select
                  v-model:value="taskForm.brand"
                  :options="brandOptions"
                  placeholder="请选择品牌"
                />
              </n-form-item>
              <n-form-item label="截止日期">
                <n-date-picker v-model:value="taskForm.deadline" type="date" />
              </n-form-item>
              <n-form-item label="任务描述">
                <n-input
                  v-model:value="taskForm.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入任务描述"
                />
              </n-form-item>
            </n-form>
          </div>
          <!-- 其他任务类型的表单可以类似添加 -->
        </div>
        <template #footer>
          <div class="modal-footer">
            <n-button @click="showTaskModal = false">取消</n-button>
            <n-button type="primary" @click="saveTask">保存任务</n-button>
          </div>
        </template>
      </n-modal>
    </div>
  </CommonPage>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import {
  NIcon,
  NCard,
  NButton,
  NModal,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NDatePicker,
  NDataTable,
  NProgress,
  NTag,
  useMessage,
} from 'naive-ui'
import {
  Car,
  Package,
  FileText,
  Bulb,
  ListCheck,
  Clock,
  Check,
  AlertTriangle,
  ArrowUp,
  ArrowDown,
  ArrowRight,
} from '@vicons/tabler'
import CommonPage from '@/components/page/CommonPage.vue'
import { useUserStore } from '@/store'

// 获取用户store和消息组件
const userStore = useUserStore()
const message = useMessage()

// 用户信息计算属性
const userInfo = computed(() => ({
  name: userStore.name || '王管理员',
  email: userStore.email || '',
  avatar: userStore.avatar || '',
  roles: userStore.role || [],
  isSuperUser: userStore.isSuperUser || false,
  isActive: userStore.isActive !== false,
  userId: userStore.userId || '',
}))

// 快速创建任务数据
const quickTasks = ref([
  {
    id: 1,
    type: '车型+OE数据',
    title: '车型+OE数据任务',
    description: '创建新的车型和OE码数据采集与整理任务',
    icon: Car,
    colorClass: 'task-card-primary',
    iconBgClass: 'task-icon-primary',
    textColorClass: 'task-text-primary',
  },
  {
    id: 2,
    type: '产品管理',
    title: '产品管理任务',
    description: '创建新产品录入或现有产品信息修改任务',
    icon: Package,
    colorClass: 'task-card-secondary',
    iconBgClass: 'task-icon-secondary',
    textColorClass: 'task-text-secondary',
  },
  {
    id: 3,
    type: '询报价',
    title: '询报价任务',
    description: '创建新的询价单或处理报价相关任务',
    icon: FileText,
    colorClass: 'task-card-warning',
    iconBgClass: 'task-icon-warning',
    textColorClass: 'task-text-warning',
  },
  {
    id: 4,
    type: '新产品开发',
    title: '新产品开发任务',
    description: '创建新产品研发与开发相关任务',
    icon: Bulb,
    colorClass: 'task-card-success',
    iconBgClass: 'task-icon-success',
    textColorClass: 'task-text-success',
  },
])

// 任务统计数据
const taskStats = ref([
  {
    label: '总任务数',
    value: '328',
    icon: ListCheck,
    iconColor: '#165DFF',
    iconBgClass: 'stat-icon-primary',
    trend: '12%',
    trendText: '较上月',
    trendIcon: ArrowUp,
    trendClass: 'trend-success',
    isDanger: false,
  },
  {
    label: '进行中任务',
    value: '86',
    icon: Clock,
    iconColor: '#FF7D00',
    iconBgClass: 'stat-icon-warning',
    trend: '5%',
    trendText: '较上周',
    trendIcon: ArrowUp,
    trendClass: 'trend-danger',
    isDanger: false,
  },
  {
    label: '已完成任务',
    value: '215',
    icon: Check,
    iconColor: '#00B42A',
    iconBgClass: 'stat-icon-success',
    trend: '18%',
    trendText: '较上月',
    trendIcon: ArrowUp,
    trendClass: 'trend-success',
    isDanger: false,
  },
  {
    label: '逾期任务',
    value: '7',
    icon: AlertTriangle,
    iconColor: '#F53F3F',
    iconBgClass: 'stat-icon-danger',
    trend: '3%',
    trendText: '较上周',
    trendIcon: ArrowDown,
    trendClass: 'trend-success',
    isDanger: true,
  },
])

// 图表相关数据
const activeChartTab = ref('本月')
const chartTabs = ref([
  { key: '本月', label: '本月' },
  { key: '上月', label: '上月' },
  { key: '全年', label: '全年' },
])

// 图表配置
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: {
    data: ['车型+OE数据任务', '产品管理任务', '询报价任务', '新产品开发任务'],
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月'],
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      name: '车型+OE数据任务',
      type: 'bar',
      data: [18, 25, 22, 30, 28, 35],
      itemStyle: { color: '#165DFF', borderRadius: [4, 4, 0, 0] },
    },
    {
      name: '产品管理任务',
      type: 'bar',
      data: [15, 20, 18, 22, 25, 28],
      itemStyle: { color: '#36BFFA', borderRadius: [4, 4, 0, 0] },
    },
    {
      name: '询报价任务',
      type: 'bar',
      data: [12, 15, 18, 16, 20, 22],
      itemStyle: { color: '#FF7D00', borderRadius: [4, 4, 0, 0] },
    },
    {
      name: '新产品开发任务',
      type: 'bar',
      data: [8, 10, 12, 15, 10, 14],
      itemStyle: { color: '#00B42A', borderRadius: [4, 4, 0, 0] },
    },
  ],
}))

// 任务进度数据
const taskProgress = ref([
  { name: '车型+OE数据任务', percent: 75, color: '#165DFF' },
  { name: '产品管理任务', percent: 62, color: '#36BFFA' },
  { name: '询报价任务', percent: 48, color: '#FF7D00' },
  { name: '新产品开发任务', percent: 35, color: '#00B42A' },
])

// 表格数据
const taskColumns = [
  {
    title: '任务名称',
    key: 'name',
    width: 200,
  },
  {
    title: '任务类型',
    key: 'type',
    width: 120,
    render(row) {
      const typeMap = {
        '车型+OE': { color: '#165DFF', bg: 'rgba(22, 93, 255, 0.1)' },
        产品管理: { color: '#36BFFA', bg: 'rgba(54, 191, 250, 0.1)' },
        询报价: { color: '#FF7D00', bg: 'rgba(255, 125, 0, 0.1)' },
        新产品开发: { color: '#00B42A', bg: 'rgba(0, 180, 42, 0.1)' },
      }
      const style = typeMap[row.type] || { color: '#666', bg: '#f0f0f0' }
      return h(
        'span',
        {
          style: {
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '12px',
            color: style.color,
            backgroundColor: style.bg,
          },
        },
        row.type
      )
    },
  },
  {
    title: '负责人',
    key: 'assignee',
    width: 120,
    render(row) {
      return h('div', { style: { display: 'flex', alignItems: 'center' } }, [
        h('img', {
          src: row.avatar,
          alt: '头像',
          style: {
            width: '24px',
            height: '24px',
            borderRadius: '50%',
            marginRight: '8px',
          },
        }),
        h('span', { style: { fontSize: '14px' } }, row.assignee),
      ])
    },
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap = {
        进行中: { color: '#FF7D00', bg: 'rgba(255, 125, 0, 0.1)' },
        已完成: { color: '#00B42A', bg: 'rgba(0, 180, 42, 0.1)' },
        待处理: { color: '#86909C', bg: 'rgba(134, 144, 156, 0.1)' },
      }
      const style = statusMap[row.status] || { color: '#666', bg: '#f0f0f0' }
      return h(
        'span',
        {
          style: {
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '12px',
            color: style.color,
            backgroundColor: style.bg,
          },
        },
        row.status
      )
    },
  },
  {
    title: '截止日期',
    key: 'deadline',
    width: 120,
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render(row) {
      return h('div', { style: { display: 'flex', gap: '8px' } }, [
        h(
          'button',
          {
            style: { color: '#165DFF', background: 'none', border: 'none', cursor: 'pointer' },
            onClick: () => editTask(row),
          },
          '编辑'
        ),
        h(
          'button',
          {
            style: { color: '#86909C', background: 'none', border: 'none', cursor: 'pointer' },
            onClick: () => viewTask(row),
          },
          '详情'
        ),
      ])
    },
  },
]

const recentTasks = ref([
  {
    id: 1,
    name: '2023款车型数据录入',
    type: '车型+OE',
    assignee: '李工程师',
    avatar: 'https://picsum.photos/id/1012/32/32',
    status: '进行中',
    deadline: '2023-07-15',
  },
  {
    id: 2,
    name: '制动系统产品信息更新',
    type: '产品管理',
    assignee: '陈专员',
    avatar: 'https://picsum.photos/id/1066/32/32',
    status: '已完成',
    deadline: '2023-07-05',
  },
  {
    id: 3,
    name: '北京汽车配件询价',
    type: '询报价',
    assignee: '刘销售',
    avatar: 'https://picsum.photos/id/1074/32/32',
    status: '待处理',
    deadline: '2023-07-20',
  },
  {
    id: 4,
    name: '新能源汽车电池配件开发',
    type: '新产品开发',
    assignee: '郑经理',
    avatar: 'https://picsum.photos/id/1083/32/32',
    status: '进行中',
    deadline: '2023-09-30',
  },
])

const tablePagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
}

// 模态框相关数据
const showTaskModal = ref(false)
const currentTaskType = ref('')
const modalTitle = computed(() => `创建${currentTaskType.value}任务`)

const taskForm = ref({
  name: '',
  brand: '',
  deadline: null,
  description: '',
})

const brandOptions = [
  { label: '奔驰', value: '1' },
  { label: '宝马', value: '2' },
  { label: '奥迪', value: '3' },
  { label: '大众', value: '4' },
  { label: '丰田', value: '5' },
  { label: '其他', value: '6' },
]

// 方法
const openTaskModal = (taskType) => {
  currentTaskType.value = taskType
  showTaskModal.value = true
  // 重置表单
  taskForm.value = {
    name: '',
    brand: '',
    deadline: null,
    description: '',
  }
}

const saveTask = () => {
  // 这里添加保存任务的逻辑
  message.success('任务创建成功！')
  showTaskModal.value = false
}

const editTask = (task) => {
  message.info(`编辑任务: ${task.name}`)
}

const viewTask = (task) => {
  message.info(`查看任务详情: ${task.name}`)
}
</script>

<style scoped>
.workbench-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: clamp(1.25rem, 3vw, 1.75rem);
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1d2129;
}

.page-subtitle {
  color: #86909c;
  margin: 0;
  font-size: 14px;
}

/* 快速创建任务 */
.quick-tasks {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.task-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-left: 4px solid transparent;
}

.task-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.task-card-primary {
  border-left-color: #165dff;
}

.task-card-secondary {
  border-left-color: #36bffa;
}

.task-card-warning {
  border-left-color: #ff7d00;
}

.task-card-success {
  border-left-color: #00b42a;
}

.task-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.task-icon-primary {
  background: rgba(22, 93, 255, 0.1);
  color: #165dff;
}

.task-icon-secondary {
  background: rgba(54, 191, 250, 0.1);
  color: #36bffa;
}

.task-icon-warning {
  background: rgba(255, 125, 0, 0.1);
  color: #ff7d00;
}

.task-icon-success {
  background: rgba(0, 180, 42, 0.1);
  color: #00b42a;
}

.task-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: #1d2129;
}

.task-description {
  color: #86909c;
  font-size: 14px;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.task-action {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  gap: 8px;
}

.task-text-primary {
  color: #165dff;
}

.task-text-secondary {
  color: #36bffa;
}

.task-text-warning {
  color: #ff7d00;
}

.task-text-success {
  color: #00b42a;
}

/* 任务统计 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.stat-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.stat-text {
  flex: 1;
}

.stat-label {
  color: #86909c;
  font-size: 14px;
  margin: 0 0 4px 0;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #1d2129;
}

.stat-value.text-danger {
  color: #f53f3f;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon-primary {
  background: rgba(22, 93, 255, 0.1);
}

.stat-icon-warning {
  background: rgba(255, 125, 0, 0.1);
}

.stat-icon-success {
  background: rgba(0, 180, 42, 0.1);
}

.stat-icon-danger {
  background: rgba(245, 63, 63, 0.1);
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
}

.trend-success {
  color: #00b42a;
}

.trend-danger {
  color: #f53f3f;
}

.trend-text {
  color: #86909c;
  font-weight: normal;
}

/* 图表和进度区域 */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-card,
.progress-card,
.task-table-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chart-title,
.progress-title,
.table-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #1d2129;
}

.chart-tabs {
  display: flex;
  gap: 8px;
}

.chart-container {
  padding: 16px 0;
}

.progress-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 24px;
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-name {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
}

.progress-percent {
  font-size: 14px;
  color: #86909c;
}

.overall-progress {
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.overall-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 16px 0;
  color: #1d2129;
}

.overall-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.overall-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.overall-percent {
  font-size: 14px;
  font-weight: 600;
  color: #00b42a;
}

/* 表格区域 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 模态框 */
.task-form {
  padding: 16px 0;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .charts-section {
    grid-template-columns: 1fr;
  }

  .quick-tasks {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .workbench-container {
    padding: 16px;
  }

  .quick-tasks {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
