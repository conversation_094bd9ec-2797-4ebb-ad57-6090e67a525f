<template>
  <div class="flow-editor">
    <!-- 顶部工具栏 -->
    <div class="flow-toolbar">
      <n-space>
        <n-button type="primary" @click="saveFlow">
          <template #icon>
            <n-icon><SaveOutlined /></n-icon>
          </template>
          保存流程
        </n-button>
        <n-button @click="previewFlow">
          <template #icon>
            <n-icon><EyeOutlined /></n-icon>
          </template>
          预览
        </n-button>
        <n-button type="info" @click="executeFlow">
          <template #icon>
            <n-icon><PlayCircleOutlined /></n-icon>
          </template>
          执行流程
        </n-button>
        <n-button @click="clearFlow">
          <template #icon>
            <n-icon><ClearOutlined /></n-icon>
          </template>
          清空
        </n-button>
        <n-divider vertical />
        <n-button @click="zoomIn">
          <template #icon>
            <n-icon><ZoomInOutlined /></n-icon>
          </template>
        </n-button>
        <n-button @click="zoomOut">
          <template #icon>
            <n-icon><ZoomOutOutlined /></n-icon>
          </template>
        </n-button>
        <n-button @click="zoomToFit">适应画布</n-button>
      </n-space>
    </div>

    <div class="flow-content">
      <!-- 左侧工具面板 -->
      <div class="flow-sidebar">
        <div class="sidebar-section">
          <h4>任务节点</h4>
          <div class="node-item" @mousedown="startDrag('start')">
            <div class="node-icon start-node">
              <n-icon><PlayCircleOutlined /></n-icon>
            </div>
            <span>开始任务</span>
          </div>
          <div class="node-item" @mousedown="startDrag('vehicle')">
            <div class="node-icon vehicle-node">
              <n-icon><CarOutlined /></n-icon>
            </div>
            <span>车型任务</span>
          </div>
          <div class="node-item" @mousedown="startDrag('oe')">
            <div class="node-icon oe-node">
              <n-icon><ToolOutlined /></n-icon>
            </div>
            <span>OE任务</span>
          </div>
          <div class="node-item" @mousedown="startDrag('product')">
            <div class="node-icon product-node">
              <n-icon><ShoppingOutlined /></n-icon>
            </div>
            <span>产品任务</span>
          </div>
          <div class="node-item" @mousedown="startDrag('end')">
            <div class="node-icon end-node">
              <n-icon><CheckCircleOutlined /></n-icon>
            </div>
            <span>结束任务</span>
          </div>
        </div>

        <div class="sidebar-section">
          <h4>任务模板</h4>
          <div class="template-item" @click="loadTemplate('standard')">
            <div class="template-icon">
              <n-icon><FileTextOutlined /></n-icon>
            </div>
            <span>标准流程</span>
          </div>
          <div class="template-item" @click="loadTemplate('quick')">
            <div class="template-icon">
              <n-icon><ThunderboltOutlined /></n-icon>
            </div>
            <span>快速流程</span>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="flow-canvas">
        <div ref="containerRef" class="x6-container"></div>
      </div>

      <!-- 右侧属性面板 -->
      <div v-show="selectedNode" class="flow-properties">
        <div class="properties-header">
          <h4>节点属性</h4>
        </div>
        <div class="properties-content">
          <n-form v-if="selectedNode" :model="nodeForm" label-placement="top">
            <n-form-item label="任务名称">
              <n-input
                v-model:value="nodeForm.name"
                placeholder="请输入任务名称"
                @blur="updateNodeName"
              />
            </n-form-item>

            <n-form-item label="任务类型">
              <n-input :value="getTaskTypeLabel(nodeForm.type)" readonly />
            </n-form-item>

            <n-form-item label="任务描述">
              <n-input
                v-model:value="nodeForm.description"
                type="textarea"
                placeholder="请输入任务描述"
                @blur="updateNodeData"
              />
            </n-form-item>

            <n-form-item label="负责人">
              <n-input
                v-model:value="nodeForm.assignee"
                placeholder="请输入负责人"
                @blur="updateNodeData"
              />
            </n-form-item>

            <n-form-item label="预估时间(小时)">
              <n-input-number
                v-model:value="nodeForm.estimatedTime"
                placeholder="预估完成时间"
                :min="0"
                :step="0.5"
                @blur="updateNodeData"
              />
            </n-form-item>

            <n-form-item label="优先级">
              <n-select
                v-model:value="nodeForm.priority"
                :options="[
                  { label: '低', value: 'low' },
                  { label: '中', value: 'medium' },
                  { label: '高', value: 'high' },
                  { label: '紧急', value: 'urgent' },
                ]"
                @update:value="updateNodeData"
              />
            </n-form-item>

            <n-form-item label="任务状态">
              <n-select
                v-model:value="nodeForm.status"
                :options="[
                  { label: '待处理', value: 'pending' },
                  { label: '进行中', value: 'in_progress' },
                  { label: '已完成', value: 'completed' },
                  { label: '已暂停', value: 'paused' },
                ]"
                @update:value="updateNodeData"
              />
            </n-form-item>

            <n-divider />

            <n-form-item label="位置坐标">
              <n-space>
                <n-input-number
                  v-model:value="nodeForm.left"
                  placeholder="X"
                  size="small"
                  style="width: 80px"
                  @blur="updateNodePosition"
                />
                <n-input-number
                  v-model:value="nodeForm.top"
                  placeholder="Y"
                  size="small"
                  style="width: 80px"
                  @blur="updateNodePosition"
                />
              </n-space>
            </n-form-item>

            <!-- 删除节点按钮 -->
            <n-form-item>
              <n-button type="error" ghost style="width: 100%" @click="deleteSelectedNode">
                <template #icon>
                  <n-icon><DeleteOutlined /></n-icon>
                </template>
                删除任务节点
              </n-button>
            </n-form-item>
          </n-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, h, createApp } from 'vue'
import { Graph, Shape } from '@antv/x6'
import { useMessage, useDialog } from 'naive-ui'
import {
  SaveOutlined,
  EyeOutlined,
  ClearOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  PlayCircleOutlined,
  CarOutlined,
  ToolOutlined,
  ShoppingOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  ThunderboltOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'

// 使用 Naive UI 组件
const message = useMessage()
const dialog = useDialog()

// 响应式数据
const containerRef = ref()
const selectedNode = ref(null)
const graph = ref(null)
const nodeForm = ref({
  name: '',
  type: '',
  top: 0,
  left: 0,
  description: '',
  assignee: '',
  estimatedTime: '',
  priority: 'medium',
  status: 'pending',
})

// 节点类型配置
const nodeTypes = {
  start: {
    shape: 'rect',
    width: 120,
    height: 40,
    attrs: {
      body: {
        fill: '#52C41A',
        stroke: '#52C41A',
        rx: 20,
        ry: 20,
      },
      text: {
        fill: 'white',
        fontSize: 12,
      },
    },
  },
  vehicle: {
    shape: 'rect',
    width: 120,
    height: 40,
    attrs: {
      body: {
        fill: '#1890FF',
        stroke: '#1890FF',
        rx: 6,
        ry: 6,
      },
      text: {
        fill: 'white',
        fontSize: 12,
      },
    },
  },
  oe: {
    shape: 'rect',
    width: 120,
    height: 40,
    attrs: {
      body: {
        fill: '#722ED1',
        stroke: '#722ED1',
        rx: 6,
        ry: 6,
      },
      text: {
        fill: 'white',
        fontSize: 12,
      },
    },
  },
  product: {
    shape: 'rect',
    width: 120,
    height: 40,
    attrs: {
      body: {
        fill: '#FA8C16',
        stroke: '#FA8C16',
        rx: 6,
        ry: 6,
      },
      text: {
        fill: 'white',
        fontSize: 12,
      },
    },
  },
  end: {
    shape: 'rect',
    width: 120,
    height: 40,
    attrs: {
      body: {
        fill: '#F5222D',
        stroke: '#F5222D',
        rx: 20,
        ry: 20,
      },
      text: {
        fill: 'white',
        fontSize: 12,
      },
    },
  },
}

// 初始化图形编辑器
const initGraph = () => {
  graph.value = new Graph({
    container: containerRef.value,
    width: containerRef.value.offsetWidth,
    height: containerRef.value.offsetHeight,
    grid: {
      visible: true,
      type: 'doubleMesh',
      args: [
        {
          color: '#eee',
          thickness: 1,
        },
        {
          color: '#ddd',
          thickness: 1,
          factor: 4,
        },
      ],
    },
    selecting: {
      enabled: true,
      rubberband: true,
      showNodeSelectionBox: true,
    },
    connecting: {
      router: 'manhattan',
      connector: {
        name: 'rounded',
        args: {
          radius: 8,
        },
      },
      anchor: 'center',
      connectionPoint: 'anchor',
      allowBlank: false,
      snap: {
        radius: 20,
      },
      createEdge() {
        return new Shape.Edge({
          attrs: {
            line: {
              stroke: '#A2B1C3',
              strokeWidth: 2,
              targetMarker: {
                name: 'block',
                width: 12,
                height: 8,
              },
            },
          },
          zIndex: 0,
        })
      },
      validateConnection({ targetMagnet }) {
        return !!targetMagnet
      },
    },
    highlighting: {
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#5F95FF',
            stroke: '#5F95FF',
          },
        },
      },
    },
    resizing: true,
    rotating: true,
    history: true,
    clipboard: true,
    keyboard: true,
  })

  // 绑定事件
  bindEvents()
}

// 绑定图形事件
const bindEvents = () => {
  // 节点选中事件
  graph.value.on('node:click', ({ node }) => {
    selectedNode.value = node
    const data = node.getData() || {}
    const position = node.position()

    nodeForm.value = {
      name: node.label || data.name || '',
      type: data.type || '',
      top: position.y,
      left: position.x,
      description: data.description || '',
      assignee: data.assignee || '',
      estimatedTime: data.estimatedTime || '',
      priority: data.priority || 'medium',
      status: data.status || 'pending',
    }
  })

  // 画布点击事件（取消选中）
  graph.value.on('blank:click', () => {
    selectedNode.value = null
    nodeForm.value = {
      name: '',
      type: '',
      top: 0,
      left: 0,
      description: '',
      assignee: '',
      estimatedTime: '',
      priority: 'medium',
      status: 'pending',
    }
  })

  // 连线创建事件
  graph.value.on('edge:connected', ({ edge }) => {
    const sourceNode = edge.getSourceNode()

    // 如果源节点是条件节点，需要设置连线标签
    if (sourceNode && sourceNode.getData()?.type === 'condition') {
      // 获取源节点的现有连线数量来决定是真值分支还是假值分支
      const outgoingEdges = graph.value.getOutgoingEdges(sourceNode) || []
      const data = sourceNode.getData() || {}

      let branchLabel = ''
      if (outgoingEdges.length === 1) {
        // 第一条连线，使用真值分支
        branchLabel = data.trueBranch || '是'
      } else if (outgoingEdges.length === 2) {
        // 第二条连线，使用假值分支
        branchLabel = data.falseBranch || '否'
      } else {
        // 超过两条连线，让用户手动设置
        showConditionDialog(edge)
        return
      }

      // 自动设置连线标签
      edge.setLabels([
        {
          attrs: {
            text: {
              text: branchLabel,
              fill: '#333',
              fontSize: 12,
            },
          },
        },
      ])
    }
  })

  // 键盘删除事件
  graph.value.bindKey(['backspace', 'delete'], () => {
    const cells = graph.value.getSelectedCells()
    if (cells.length) {
      graph.value.removeCells(cells)
      // 如果删除的是当前选中的节点，清空属性面板
      if (selectedNode.value && cells.includes(selectedNode.value)) {
        selectedNode.value = null
        nodeForm.value = {
          name: '',
          type: '',
          top: 0,
          left: 0,
          condition: '',
          transformRule: '',
        }
      }
    }
  })

  // 节点右键菜单
  graph.value.on('node:contextmenu', ({ node, e }) => {
    e.preventDefault()
    showContextMenu(e, node)
  })

  // 连线右键菜单
  graph.value.on('edge:contextmenu', ({ edge, e }) => {
    e.preventDefault()
    showContextMenu(e, edge)
  })
}

// 显示右键菜单
const showContextMenu = (e, cell) => {
  const menuItems = []

  if (cell.isNode()) {
    menuItems.push({
      label: '删除节点',
      key: 'delete-node',
      icon: () => h('span', '🗑️'),
    })
    menuItems.push({
      label: '复制节点',
      key: 'copy-node',
      icon: () => h('span', '📋'),
    })
  } else if (cell.isEdge()) {
    menuItems.push({
      label: '删除连线',
      key: 'delete-edge',
      icon: () => h('span', '🗑️'),
    })
    menuItems.push({
      label: '编辑条件',
      key: 'edit-condition',
      icon: () => h('span', '✏️'),
    })
  }

  // 使用 Naive UI 的下拉菜单
  const dropdown = h(
    'div',
    {
      style: {
        position: 'fixed',
        left: e.clientX + 'px',
        top: e.clientY + 'px',
        zIndex: 9999,
        background: 'white',
        border: '1px solid #e8e8e8',
        borderRadius: '6px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        padding: '4px 0',
        minWidth: '120px',
      },
    },
    menuItems.map((item) =>
      h(
        'div',
        {
          style: {
            padding: '8px 16px',
            cursor: 'pointer',
            fontSize: '14px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          },
          onMouseenter: (e) => {
            e.target.style.background = '#f5f5f5'
          },
          onMouseleave: (e) => {
            e.target.style.background = 'transparent'
          },
          onClick: () => handleContextMenuClick(item.key, cell),
        },
        [item.icon(), item.label]
      )
    )
  )

  // 创建临时容器并添加到 body
  const container = document.createElement('div')
  document.body.appendChild(container)

  // 渲染菜单
  const app = createApp(dropdown)
  app.mount(container)

  // 点击其他地方关闭菜单
  const closeMenu = () => {
    app.unmount()
    document.body.removeChild(container)
    document.removeEventListener('click', closeMenu)
  }

  setTimeout(() => {
    document.addEventListener('click', closeMenu)
  }, 100)
}

// 处理右键菜单点击
const handleContextMenuClick = (key, cell) => {
  switch (key) {
    case 'delete-node':
    case 'delete-edge':
      graph.value.removeCell(cell)
      if (selectedNode.value === cell) {
        selectedNode.value = null
        nodeForm.value = {
          name: '',
          type: '',
          top: 0,
          left: 0,
          condition: '',
          transformRule: '',
        }
      }
      break
    case 'copy-node':
      copyNode(cell)
      break
    case 'edit-condition':
      if (cell.isEdge()) {
        showConditionDialog(cell)
      }
      break
  }
}

// 复制节点
const copyNode = (node) => {
  const position = node.position()
  const size = node.size()
  const data = node.getData()

  const newNode = graph.value.addNode({
    ...node.toJSON(),
    id: `${data.type}_${Date.now()}`,
    x: position.x + 50,
    y: position.y + 50,
    data: {
      ...data,
      name: data.name + '_copy',
    },
  })

  // 选中新节点
  graph.value.select(newNode)
}

// 显示条件设置对话框
const showConditionDialog = (edge) => {
  dialog.create({
    title: '设置连线条件',
    content: () => {
      const inputValue = ref(edge.getLabels()[0]?.attrs?.text?.text || '')
      return h('div', [
        h(
          'p',
          { style: 'margin-bottom: 12px; color: #666;' },
          '请输入条件标签（如：是/否、通过/不通过）：'
        ),
        h('input', {
          value: inputValue.value,
          onInput: (e) => {
            inputValue.value = e.target.value
          },
          style:
            'width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;',
          placeholder: '请输入条件...',
        }),
      ])
    },
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      const input = document.querySelector('.n-dialog input')
      const condition = input?.value?.trim()
      if (condition) {
        edge.setLabels([
          {
            attrs: {
              text: {
                text: condition,
                fill: '#333',
                fontSize: 12,
              },
            },
          },
        ])
        message.success('条件设置成功')
      }
    },
  })
}

// 获取节点标签
const getNodeLabel = (type) => {
  const labels = {
    start: '开始任务',
    vehicle: '车型任务',
    oe: 'OE任务',
    product: '产品任务',
    end: '结束任务',
  }
  return labels[type] || type
}

// 获取任务类型标签
const getTaskTypeLabel = (type) => {
  const labels = {
    start: '开始任务',
    vehicle: '车型数据补充任务',
    oe: 'OE数据补充任务',
    product: '产品数据补充任务',
    end: '结束任务',
  }
  return labels[type] || type
}

// 工具栏方法
const saveFlow = () => {
  const nodes = graph.value.getNodes()
  const edges = graph.value.getEdges()

  if (nodes.length === 0) {
    message.warning('请先添加任务节点')
    return
  }

  // 转换为后端需要的格式
  const flowData = {
    name: '任务流程',
    description: '数据补充任务流程',
    version: '1.0',
    nodes: nodes.map((node) => {
      const data = node.getData() || {}
      const position = node.position()
      return {
        id: node.id,
        type: data.type,
        name: data.name || node.label,
        description: data.description || '',
        assignee: data.assignee || '',
        estimatedTime: data.estimatedTime || 0,
        priority: data.priority || 'medium',
        status: data.status || 'pending',
        position: {
          x: position.x,
          y: position.y,
        },
      }
    }),
    edges: edges.map((edge) => {
      const labels = edge.getLabels()
      return {
        id: edge.id,
        source: edge.getSourceCellId(),
        target: edge.getTargetCellId(),
        label: labels.length > 0 ? labels[0].attrs?.text?.text : '',
      }
    }),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }

  console.log('保存任务流程数据:', flowData)

  // 这里可以调用后端API保存数据
  // await api.saveTaskFlow(flowData)

  message.success('任务流程保存成功')
}

const previewFlow = () => {
  const data = graph.value.toJSON()
  console.log('预览流程:', data)
  message.info('预览功能开发中')
}

// 执行任务流程演示
const executeFlow = () => {
  const nodes = graph.value.getNodes()
  const edges = graph.value.getEdges()

  if (nodes.length === 0) {
    message.warning('请先添加任务节点')
    return
  }

  // 找到开始节点
  const startNodes = nodes.filter((node) => {
    const data = node.getData() || {}
    return data.type === 'start'
  })

  if (startNodes.length === 0) {
    message.warning('请添加开始任务节点')
    return
  }

  message.info('开始执行任务流程演示...')

  // 从第一个开始节点开始执行
  executeTaskNode(startNodes[0], edges)
}

// 执行任务节点
const executeTaskNode = async (node, edges, depth = 0) => {
  if (depth > 10) {
    message.error('任务流程执行深度过深，可能存在循环')
    return
  }

  // 高亮当前节点
  node.attr('body/stroke', '#ff4d4f')
  node.attr('body/strokeWidth', 3)

  const nodeData = node.getData() || {}
  const nodeType = nodeData.type

  // 模拟任务处理时间
  await new Promise((resolve) => setTimeout(resolve, 1500))

  // 根据任务类型显示不同的消息
  switch (nodeType) {
    case 'start':
      message.success(`${nodeData.name || '开始任务'}: 任务流程启动`)
      break

    case 'vehicle':
      message.success(`${nodeData.name || '车型任务'}: 车型数据补充完成`)
      break

    case 'oe':
      message.success(`${nodeData.name || 'OE任务'}: OE数据补充完成`)
      break

    case 'product':
      message.success(`${nodeData.name || '产品任务'}: 产品数据补充完成`)
      break

    case 'end':
      message.success(`${nodeData.name || '结束任务'}: 任务流程完成`)
      // 恢复节点样式后直接返回
      setTimeout(() => {
        node.attr('body/stroke', '#f5222d')
        node.attr('body/strokeWidth', 1)
      }, 1000)
      return
  }

  // 恢复节点样式
  setTimeout(() => {
    const originalColor = getNodeColor(nodeType)
    node.attr('body/stroke', originalColor)
    node.attr('body/strokeWidth', 1)
  }, 1000)

  // 找到下一个任务节点并执行
  const outgoingEdges = edges.filter((edge) => edge.getSourceNode() === node)

  for (const edge of outgoingEdges) {
    const targetNode = edge.getTargetNode()
    if (targetNode) {
      setTimeout(() => {
        executeTaskNode(targetNode, edges, depth + 1)
      }, 800)
    }
  }
}

// 获取节点原始颜色
const getNodeColor = (nodeType) => {
  const colors = {
    start: '#52C41A',
    vehicle: '#1890FF',
    oe: '#722ED1',
    product: '#FA8C16',
    end: '#F5222D',
  }
  return colors[nodeType] || '#5F95FF'
}

// 执行单个节点
const executeNode = async (node, data, edges, depth = 0) => {
  if (depth > 10) {
    message.error('流程执行深度过深，可能存在循环')
    return
  }

  // 高亮当前节点
  node.attr('body/stroke', '#ff4d4f')
  node.attr('body/strokeWidth', 3)

  const nodeData = node.getData() || {}
  const nodeType = nodeData.type

  // 模拟节点处理时间
  await new Promise((resolve) => setTimeout(resolve, 1000))

  let processedData = { ...data }
  let shouldContinue = true

  // 根据节点类型处理数据
  switch (nodeType) {
    case 'start':
    case 'import':
      message.success(`${nodeData.name || '开始节点'}: 数据输入完成`)
      break

    case 'transform':
      if (nodeData.transformRule) {
        message.success(`${nodeData.name || '数据转换'}: 应用转换规则`)
        // 这里可以实际执行转换规则
        processedData.transformed = true
      }
      break

    case 'filter':
      if (nodeData.filterRule) {
        // 简单的条件判断示例
        if (nodeData.filterRule.includes('status') && data.status !== 'active') {
          shouldContinue = false
          message.warning(`${nodeData.name || '数据清洗'}: 数据被过滤`)
        } else {
          message.success(`${nodeData.name || '数据清洗'}: 数据通过过滤`)
        }
      }
      break

    case 'condition':
      if (nodeData.condition) {
        // 简单的条件判断示例
        let conditionResult = false
        if (nodeData.condition.includes('value > 100')) {
          conditionResult = data.value > 100
        }

        message.success(
          `${nodeData.name || '条件判断'}: 条件结果为 ${conditionResult ? '真' : '假'}`
        )

        // 根据条件结果选择不同的分支
        const outgoingEdges = edges.filter((edge) => edge.getSourceNode() === node)
        const trueBranch = outgoingEdges.find((edge) => {
          const labels = edge.getLabels()
          return (
            labels.length > 0 &&
            (labels[0].attrs?.text?.text === nodeData.trueBranch ||
              labels[0].attrs?.text?.text === '是')
          )
        })
        const falseBranch = outgoingEdges.find((edge) => {
          const labels = edge.getLabels()
          return (
            labels.length > 0 &&
            (labels[0].attrs?.text?.text === nodeData.falseBranch ||
              labels[0].attrs?.text?.text === '否')
          )
        })

        const selectedEdge = conditionResult ? trueBranch : falseBranch
        if (selectedEdge) {
          // 高亮选中的连线
          selectedEdge.attr('line/stroke', '#52c41a')
          selectedEdge.attr('line/strokeWidth', 3)

          setTimeout(() => {
            const targetNode = selectedEdge.getTargetNode()
            if (targetNode) {
              executeNode(targetNode, processedData, edges, depth + 1)
            }
          }, 500)
        }

        // 恢复节点样式
        setTimeout(() => {
          node.attr('body/stroke', nodeData.type === 'condition' ? '#FFC069' : '#5F95FF')
          node.attr('body/strokeWidth', 1)
          if (selectedEdge) {
            selectedEdge.attr('line/stroke', '#A2B1C3')
            selectedEdge.attr('line/strokeWidth', 2)
          }
        }, 2000)

        return // 条件节点特殊处理，不继续执行后续逻辑
      }
      break
  }

  // 恢复节点样式
  setTimeout(() => {
    node.attr('body/stroke', nodeData.type === 'condition' ? '#FFC069' : '#5F95FF')
    node.attr('body/strokeWidth', 1)
  }, 2000)

  // 如果应该继续执行，找到下一个节点
  if (shouldContinue && nodeType !== 'condition') {
    const outgoingEdges = edges.filter((edge) => edge.getSourceNode() === node)

    for (const edge of outgoingEdges) {
      const targetNode = edge.getTargetNode()
      if (targetNode) {
        setTimeout(() => {
          executeNode(targetNode, processedData, edges, depth + 1)
        }, 500)
      }
    }
  }
}

const clearFlow = () => {
  dialog.warning({
    title: '确认清空',
    content: '确定要清空所有节点吗？此操作不可撤销。',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      graph.value.clearCells()
      selectedNode.value = null
      message.success('画布已清空')
    },
  })
}

const zoomIn = () => {
  graph.value.zoom(0.1)
}

const zoomOut = () => {
  graph.value.zoom(-0.1)
}

const zoomToFit = () => {
  graph.value.zoomToFit({ padding: 20 })
}

// 节点属性更新方法
const updateNodeName = () => {
  if (selectedNode.value) {
    selectedNode.value.setLabel(nodeForm.value.name)
    selectedNode.value.setData({
      ...selectedNode.value.getData(),
      name: nodeForm.value.name,
    })
  }
}

const updateNodePosition = () => {
  if (selectedNode.value) {
    selectedNode.value.position(nodeForm.value.left, nodeForm.value.top)
  }
}

// 更新节点数据
const updateNodeData = () => {
  if (selectedNode.value) {
    selectedNode.value.setData({
      ...selectedNode.value.getData(),
      description: nodeForm.value.description,
      assignee: nodeForm.value.assignee,
      estimatedTime: nodeForm.value.estimatedTime,
      priority: nodeForm.value.priority,
      status: nodeForm.value.status,
    })
  }
}

// 删除选中的节点
const deleteSelectedNode = () => {
  if (selectedNode.value) {
    dialog.warning({
      title: '确认删除',
      content: '确定要删除这个节点吗？此操作不可撤销。',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        graph.value.removeCell(selectedNode.value)
        selectedNode.value = null
        nodeForm.value = {
          name: '',
          type: '',
          top: 0,
          left: 0,
          description: '',
          assignee: '',
          estimatedTime: '',
          priority: 'medium',
          status: 'pending',
        }
        message.success('节点已删除')
      },
    })
  }
}

// 加载任务流模板
const loadTemplate = (templateType) => {
  // 清空当前画布
  graph.value.clearCells()

  let nodes = []
  let edges = []

  if (templateType === 'standard') {
    // 标准流程：开始 -> 车型任务 -> OE任务 -> 产品任务 -> 结束
    nodes = [
      { id: 'start_1', type: 'start', x: 100, y: 100, name: '开始任务' },
      {
        id: 'vehicle_1',
        type: 'vehicle',
        x: 300,
        y: 100,
        name: '车型数据补充',
        assignee: '数据专员',
        estimatedTime: 2,
      },
      {
        id: 'oe_1',
        type: 'oe',
        x: 500,
        y: 100,
        name: 'OE数据补充',
        assignee: '技术专员',
        estimatedTime: 4,
      },
      {
        id: 'product_1',
        type: 'product',
        x: 700,
        y: 100,
        name: '产品数据补充',
        assignee: '产品专员',
        estimatedTime: 3,
      },
      { id: 'end_1', type: 'end', x: 900, y: 100, name: '结束任务' },
    ]

    edges = [
      { source: 'start_1', target: 'vehicle_1' },
      { source: 'vehicle_1', target: 'oe_1' },
      { source: 'oe_1', target: 'product_1' },
      { source: 'product_1', target: 'end_1' },
    ]
  } else if (templateType === 'quick') {
    // 快速流程：开始 -> 产品任务 -> 结束
    nodes = [
      { id: 'start_1', type: 'start', x: 200, y: 150, name: '开始任务' },
      {
        id: 'product_1',
        type: 'product',
        x: 500,
        y: 150,
        name: '快速产品补充',
        assignee: '产品专员',
        estimatedTime: 1,
        priority: 'high',
      },
      { id: 'end_1', type: 'end', x: 800, y: 150, name: '结束任务' },
    ]

    edges = [
      { source: 'start_1', target: 'product_1' },
      { source: 'product_1', target: 'end_1' },
    ]
  }

  // 创建节点
  nodes.forEach((nodeData) => {
    const config = nodeTypes[nodeData.type]
    const node = graph.value.addNode({
      id: nodeData.id,
      x: nodeData.x,
      y: nodeData.y,
      ...config,
      label: nodeData.name,
      data: {
        type: nodeData.type,
        name: nodeData.name,
        description: nodeData.description || '',
        assignee: nodeData.assignee || '',
        estimatedTime: nodeData.estimatedTime || '',
        priority: nodeData.priority || 'medium',
        status: 'pending',
      },
      ports: {
        groups: {
          top: {
            position: 'top',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
              },
            },
          },
          right: {
            position: 'right',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
              },
            },
          },
          bottom: {
            position: 'bottom',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
              },
            },
          },
          left: {
            position: 'left',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
              },
            },
          },
        },
        items: [{ group: 'top' }, { group: 'right' }, { group: 'bottom' }, { group: 'left' }],
      },
    })
  })

  // 创建连线
  edges.forEach((edgeData) => {
    graph.value.addEdge({
      source: edgeData.source,
      target: edgeData.target,
      attrs: {
        line: {
          stroke: '#A2B1C3',
          strokeWidth: 2,
          targetMarker: {
            name: 'block',
            width: 12,
            height: 8,
          },
        },
      },
    })
  })

  message.success(`${templateType === 'standard' ? '标准' : '快速'}流程模板加载成功`)
}

// 修复拖拽功能
const startDrag = (nodeType) => {
  // 简化拖拽实现，直接在画布中心创建节点
  const centerX = containerRef.value.offsetWidth / 2
  const centerY = containerRef.value.offsetHeight / 2

  const node = createNodeInCanvas(nodeType, centerX, centerY)
  graph.value.addNode(node)
}

// 在画布中创建节点
const createNodeInCanvas = (type, x, y) => {
  const config = nodeTypes[type]
  const nodeId = `${type}_${Date.now()}`

  return {
    id: nodeId,
    x: x - config.width / 2,
    y: y - config.height / 2,
    ...config,
    label: getNodeLabel(type),
    data: {
      type,
      name: getNodeLabel(type),
    },
    ports: {
      groups: {
        top: {
          position: 'top',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
            },
          },
        },
        right: {
          position: 'right',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
            },
          },
        },
        bottom: {
          position: 'bottom',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
            },
          },
        },
        left: {
          position: 'left',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
            },
          },
        },
      },
      items: [{ group: 'top' }, { group: 'right' }, { group: 'bottom' }, { group: 'left' }],
    },
  }
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initGraph()
  })
})
</script>

<style scoped>
.flow-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.flow-toolbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.flow-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.flow-sidebar {
  width: 200px;
  background: white;
  border-right: 1px solid #e8e8e8;
  padding: 16px;
  overflow-y: auto;
}

.sidebar-section {
  margin-bottom: 24px;
}

.sidebar-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
  background: white;
}

.node-item:hover {
  border-color: #21438c;
  box-shadow: 0 2px 8px rgba(33, 67, 140, 0.15);
  transform: translateY(-1px);
}

.node-item:active {
  cursor: grabbing;
}

.node-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: white;
  font-size: 14px;
}

.start-node {
  background: #52c41a;
}

.vehicle-node {
  background: #1890ff;
}

.oe-node {
  background: #722ed1;
}

.product-node {
  background: #fa8c16;
}

.end-node {
  background: #f5222d;
}

.template-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.template-item:hover {
  border-color: #21438c;
  box-shadow: 0 2px 8px rgba(33, 67, 140, 0.15);
  transform: translateY(-1px);
}

.template-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: #666;
  font-size: 14px;
  background: #f0f0f0;
}

.node-item span {
  font-size: 12px;
  color: #666;
}

.flow-canvas {
  flex: 1;
  position: relative;
  background: #fafafa;
}

.x6-container {
  width: 100%;
  height: 100%;
}

.flow-properties {
  width: 460px;
  background: white;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.properties-header {
  height: 50px;
  padding: 0 16px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  background: #fafafa;
}

.properties-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.properties-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* X6 样式覆盖 */
:deep(.x6-widget-selection-rubberband) {
  border: 1px solid #21438c;
  background-color: rgba(33, 67, 140, 0.1);
}

:deep(.x6-widget-selection-box) {
  opacity: 0;
}

:deep(.x6-node-selected .x6-widget-selection-box) {
  opacity: 1;
  border: 2px solid #21438c;
}

:deep(.x6-port-body) {
  visibility: hidden;
}

:deep(.x6-node:hover .x6-port-body) {
  visibility: visible;
}

:deep(.x6-edge:hover path) {
  stroke: #21438c;
  stroke-width: 3;
}

:deep(.x6-edge-selected path) {
  stroke: #21438c;
  stroke-width: 3;
}
</style>
