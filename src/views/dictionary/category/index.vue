<template>
  <CommonPage show-footer title="分类管理">
    <n-card>
      <!-- 顶部操作栏 -->
      <n-space justify="space-between" class="mb-4">
        <n-space>
          <n-button type="primary" @click="handleCreateModal"> + 新建分类 </n-button>
        </n-space>
        <n-space>
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button circle @click="loadData">
                <template #icon>
                  <n-icon><reload-outlined /></n-icon>
                </template>
              </n-button>
            </template>
            刷新数据
          </n-tooltip>
        </n-space>
      </n-space>

      <!-- 数据表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
      />

      <!-- 分类编辑弹窗 -->
      <n-modal v-model:show="showEditModal">
        <n-card
          style="width: 500px"
          :title="isEdit ? '编辑分类' : '新建分类'"
          :bordered="false"
          size="huge"
        >
          <n-form ref="formRef" :model="formData">
            <n-form-item label="分类名称" required>
              <n-input v-model:value="formData.category" placeholder="请输入名称" />
            </n-form-item>
            <n-form-item label="备注">
              <n-input
                v-model:value="formData.remark"
                type="textarea"
                placeholder="请输入备注"
                :autosize="{ minRows: 3 }"
              />
            </n-form-item>
          </n-form>
          <template #footer>
            <n-space justify="end">
              <n-button @click="showEditModal = false">取消</n-button>
              <n-button type="primary" @click="handleSubmit">{{
                isEdit ? '更新' : '创建'
              }}</n-button>
            </n-space>
          </template>
        </n-card>
      </n-modal>

      <!-- 分类详情弹窗 -->
      <n-modal v-model:show="showDetailModal">
        <n-card style="width: 1000px" title="分类详情" :bordered="false" size="huge">
          <n-data-table :columns="detailColumns" :data="detailData" :bordered="false" />
          <template #footer>
            <n-space justify="end">
              <n-button @click="showDetailModal = false">关闭</n-button>
            </n-space>
          </template>
        </n-card>
      </n-modal>
    </n-card>
  </CommonPage>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { ReloadOutlined } from '@vicons/antd'
import { NButton, NSwitch } from 'naive-ui'
import { useRouter } from 'vue-router'
import api from '@/api'

// 表格列配置
const columns = ref([
  { title: '分类名称', key: 'category' },
  { title: '备注', key: 'remark' },
  { title: '创建时间', key: 'created_at' },
  { title: '创建人', key: 'create_user' },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return h('div', { class: 'space-x-2' }, [
        h(
          NButton,
          {
            size: 'small',
            onClick: () => handleEdit(row),
          },
          '编辑'
        ),
        h(
          NButton,
          {
            size: 'small',
            onClick: () => handleViewDetails(row),
          },
          '详情'
        ),
      ])
    },
  },
])

// 详情表格列配置
const detailColumns = [
  { title: '表名称', key: 'table_name' },
  { title: '类型', key: 'purpose' },
  { title: '更新时间', key: 'updated_at' },
  { title: '创建人', key: 'create_user' },
  {
    title: '状态',
    key: 'status',
    render(row) {
      return h(NSwitch, {
        value: row.status === '1',
        loading: row._loading,
        disabled: row._loading,
        onUpdateValue: (val) => handleToggleStatus(row, val ? '1' : '0'),
      })
    },
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return h('div', { class: 'space-x-2' }, [
        h(
          NButton,
          {
            size: 'small',
            onClick: () => handleTableOperation(row),
          },
          '管理'
        ),
      ])
    },
  },
]

// 表格数据
const tableData = ref([])
const detailData = ref([])
const loading = ref(false)
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
})

// 表单相关
const showEditModal = ref(false)
const showDetailModal = ref(false)
const isEdit = ref(false)
const formData = reactive({
  id: null,
  category: '',
  remark: '',
})

// 加载主数据
async function loadData() {
  try {
    loading.value = true
    const response = await api.getCategoryTableList()
    tableData.value = response.data
  } finally {
    loading.value = false
  }
}

const handleToggleStatus = async (row, newStatus) => {
  try {
    row._loading = true
    await api.updateTableStatus({
      table_id: row.id, // 根据实际接口参数调整
      status: newStatus,
    })

    row.status = newStatus
  } finally {
    row._loading = false
  }
}

// 打开新建模态框
function handleCreateModal() {
  isEdit.value = false
  resetForm()
  showEditModal.value = true
}

// 打开编辑模态框
function handleEdit(row) {
  isEdit.value = true
  Object.assign(formData, row)
  showEditModal.value = true
}

// 查看详情
async function handleViewDetails(row) {
  const detail_res = await api.getCategoryDetailTable({ category: row.category })
  detailData.value = detail_res.data
  showDetailModal.value = true
}

// 表单提交
async function handleSubmit() {
  // 这里应调用API接口
  formData.cid = formData.id
  await api.TableCategoryHandler(formData)
  showEditModal.value = false
  await loadData()
}

// 重置表单
function resetForm() {
  formData.id = null
  formData.category = ''
  formData.remark = ''
}

const router = useRouter()
const handleTableOperation = (row) => {
  const routeData = router.resolve({
    name: 'DataManagement',
    query: {
      type: row.purpose.includes('标签') ? '标签' : row.purpose.includes('字段') ? '字段' : '',
      table: row.table_name,
    },
  })
  // 处理浏览器安全策略
  const newWindow = window.open('', '_blank')
  if (newWindow) {
    newWindow.location = routeData.href
  } else {
    console.error('无法打开新窗口，请检查浏览器设置')
  }
}

onMounted(() => {
  loadData()
})
</script>
