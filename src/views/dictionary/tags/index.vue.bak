<template>
  <CommonPage show-footer title="数据标签">
    <n-layout has-sider>
      <n-layout-sider width="400" content-style="padding: 20px;">
        <!-- 数据表选择 -->
        <n-card title="选择数据表" style="margin-bottom: 20px">
          <n-select
            v-model:value="selectedTable"
            :options="tableOptions"
            placeholder="请选择数据表"
            :loading="tableLoading"
            @update:value="handleTableChange"
          />
        </n-card>

        <!-- 字段选择 -->
        <n-card title="选择字段" style="margin-bottom: 20px">
          <n-select
            v-model:value="selectedFields"
            multiple
            :options="fieldOptions"
            placeholder="请选择字段"
            :disabled="!selectedTable"
            :loading="fieldLoading"
          />
        </n-card>

        <!-- 优化后的查询条件 -->
        <n-card title="查询条件" style="margin-bottom: 20px">
          <div v-for="(condition, index) in conditions" :key="index" class="condition-item">
            <n-space vertical>
              <n-space>
                <n-select
                  v-model:value="condition.field"
                  :options="fieldOptions"
                  placeholder="选择字段"
                  style="width: 180px"
                  :disabled="!selectedTable"
                />
                <n-select
                  v-model:value="condition.operator"
                  :options="operatorOptions"
                  style="width: 100px"
                />
                <n-input
                  v-model:value="condition.value"
                  placeholder="输入值"
                  style="width: 150px"
                />
                <n-button
                  type="error"
                  tertiary
                  style="flex-shrink: 0"
                  @click="removeCondition(index)"
                >
                  删除
                </n-button>
              </n-space>
            </n-space>
          </div>
          <n-button
            type="primary"
            tertiary
            style="margin-top: 10px; width: 100%"
            @click="addCondition"
          >
            添加条件
          </n-button>
        </n-card>

        <n-button
          type="primary"
          style="margin-top: 20px; width: 100%"
          :disabled="!selectedTable"
          :loading="queryLoading"
          @click="handleQuery"
        >
          执行查询
        </n-button>
      </n-layout-sider>

      <!-- 右侧结果区域 -->
      <n-layout-content content-style="padding: 20px;">
        <n-card title="查询结果">
          <n-data-table
            v-model:checked-row-keys="selectedRowKeys"
            :columns="resultColumns"
            :data="queryResult"
            :row-key="(row) => row.id"
            :pagination="pagination"
            :loading="resultLoading"
          />

          <!-- 带搜索的标签选择 -->
          <div style="margin-top: 20px">
            <n-space vertical>
              <n-tree-select
                v-model:value="selectedTag"
                :options="tagTreeData"
                placeholder="搜索并选择标签"
                clearable
                filterable
                label-field="label"
                key-field="key"
                children-field="children"
                style="max-width: 400px"
                @update:value="handleTreeSelect"
              />
              <n-button
                type="primary"
                :disabled="!selectedRowKeys.length || !selectedTag"
                :loading="tagApplying"
                @click="applyTag"
              >
                应用选中标签
              </n-button>
            </n-space>
          </div>
        </n-card>
      </n-layout-content>
    </n-layout>
  </CommonPage>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NCard,
  NSelect,
  NInput,
  NButton,
  NSpace,
  NDataTable,
  useMessage,
} from 'naive-ui'
import api from '@/api'

const message = useMessage()

// 响应式状态
const selectedTable = ref(null)
const selectedFields = ref([])
const conditions = ref([{ field: '', operator: '=', value: '' }])
const queryResult = ref([])
const selectedRowKeys = ref([])
const selectedTag = ref(null)

// 加载状态
const tableLoading = ref(false)
const fieldLoading = ref(false)
const queryLoading = ref(false)
const resultLoading = ref(false)
const tagLoading = ref(false)
const tagApplying = ref(false)

// 数据缓存
const tableOptions = ref([])
const fieldOptions = ref([])
const tagTreeData = ref([])
const filteredTagOptions = ref([])

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page) => (pagination.value.page = page),
  onUpdatePageSize: (size) => {
    pagination.value.pageSize = size
    pagination.value.page = 1
  },
})

// 静态选项
const operatorOptions = ref([
  { label: '等于', value: '=' },
  { label: '不等于', value: '!=' },
  { label: '包含', value: 'LIKE' },
  { label: '大于', value: '>' },
  { label: '小于', value: '<' },
])

// 计算属性
const resultColumns = computed(() => {
  const columns = selectedFields.value.map((field) => ({
    title: field.label,
    key: field.value,
    ellipsis: { tooltip: true },
  }))

  columns.push({
    title: '标签',
    key: 'tags',
    render: (row) => row.tags?.map((t) => t.name).join(', ') || '无',
  })

  return [{ type: 'selection' }, ...columns]
})

// 方法
const fetchTables = async () => {
  try {
    tableLoading.value = true
    const { data } = await api.getTableList()
    tableOptions.value = data.map((t) => ({ label: t, value: t }))
  } catch (error) {
    message.error('获取数据表失败: ' + error.message)
  } finally {
    tableLoading.value = false
  }
}

const fetchFields = async (tableId) => {
  try {
    fieldLoading.value = true
    const { data } = await api.getTableFieldsList({ table_name: tableId })
    fieldOptions.value = data.map((f) => ({ label: f, value: f }))
  } catch (error) {
    message.error('获取字段失败: ' + error.message)
  } finally {
    fieldLoading.value = false
  }
}

const fetchTags = async () => {
  try {
    tagLoading.value = true
    const { data } = await api.getCategoryList()

    // 递归转换数据结构
    const transformTree = (nodes) => {
      return nodes.map((node) => ({
        label: node.label,
        key: node.id, // 使用 id 作为节点的唯一标识
        children: node.children ? transformTree(node.children) : undefined,
      }))
    }

    tagTreeData.value = transformTree(data)
  } catch (error) {
    message.error('标签加载失败: ' + error.message)
  } finally {
    tagLoading.value = false
  }
}

const handleTreeSelect = (value) => {
  selectedTag.value = value
}

const handleTableChange = (tableId) => {
  selectedFields.value = []
  conditions.value = [{ field: '', operator: '=', value: '' }]
  fetchFields(tableId)
}

const addCondition = () => {
  conditions.value.push({ field: '', operator: '=', value: '' })
}

const removeCondition = (index) => {
  conditions.value.splice(index, 1)
}

const handleQuery = async () => {
  try {
    queryLoading.value = true
    resultLoading.value = true

    const params = {
      table: selectedTable.value,
      fields: selectedFields.value.map((f) => f.value),
      conditions: conditions.value
        .filter((c) => c.field && c.value)
        .map((c) => ({
          field: c.field.value,
          operator: c.operator,
          value: c.value,
        })),
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
    }

    const { data } = await axios.post('/api/query', params)
    queryResult.value = data.items
    pagination.value.itemCount = data.total
    message.success(`查询到 ${data.total} 条结果`)
  } catch (error) {
    message.error('查询失败: ' + error.message)
  } finally {
    queryLoading.value = false
    resultLoading.value = false
  }
}

const applyTag = async () => {
  try {
    tagApplying.value = true
    await axios.post('/api/tags/apply', {
      recordIds: selectedRowKeys.value,
      tagId: selectedTag.value,
    })

    // 更新本地数据
    queryResult.value = queryResult.value.map((row) => {
      if (selectedRowKeys.value.includes(row.id)) {
        const tag = filteredTagOptions.value.find((t) => t.value === selectedTag.value)
        if (tag && !row.tags.some((t) => t.id === tag.value)) {
          row.tags.push({ id: tag.value, name: tag.label })
        }
      }
      return row
    })

    message.success('标签应用成功')
    selectedRowKeys.value = []
    selectedTag.value = null
  } catch (error) {
    message.error('标签应用失败: ' + error.message)
  } finally {
    tagApplying.value = false
  }
}

// 初始化
onMounted(() => {
  fetchTables()
})
// 初始化时获取标签数据
onMounted(async () => {
  fetchTags()
})
</script>

<style scoped>
.condition-item {
  margin-bottom: 12px;
  padding: 8px;
  background: #f8f8f8;
  border-radius: 4px;
}
</style>
