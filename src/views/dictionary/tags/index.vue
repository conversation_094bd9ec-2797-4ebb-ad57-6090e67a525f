<template>
    <CommonPage show-footer title="数据标签">
    <n-message-provider>
      <n-config-provider :theme-overrides="{ common: { fontWeightStrong: '600' } }">
        <n-space vertical :size="20" style="padding: 20px;">
          <n-grid :x-gap="20" :y-gap="20" :cols="24">
            <!-- Left Panel: Query Configuration -->
            <n-gi :span="8">
              <n-card title="选择数据表">
                <n-select
                  v-model:value="selectedTable"
                  :options="tableOptions"
                  placeholder="请选择数据表"
                  :loading="loadingTables"
                  clearable
                />
              </n-card>
  
              <n-card title="选择字段" style="margin-top: 20px;">
                <n-select
                  v-model:value="selectedDisplayFields"
                  :options="fieldOptionsForSelect"
                  multiple
                  filterable
                  placeholder="请选择需要展示的字段 (可多选)"
                  :disabled="!selectedTable || loadingFields"
                  :loading="loadingFields"
                  clearable
                />
              </n-card>
  
              <n-card title="查询条件" style="margin-top: 20px;">
                <n-space vertical>
                  <n-space v-for="(condition, index) in queryConditions" :key="condition.id" align="center">
                    <n-select
                      v-model:value="condition.field"
                      :options="fieldOptionsForSelect"
                      placeholder="字段"
                      style="min-width: 120px;"
                      :disabled="!selectedTable || loadingFields"
                      clearable
                    />
                    <n-select
                      v-model:value="condition.operator"
                      :options="availableOperators"
                      placeholder="操作"
                      style="min-width: 100px;"
                      clearable
                    />
                    <n-input
                      v-model:value="condition.value"
                      placeholder="输入值"
                    />
                    <n-button type="error" ghost @click="removeCondition(index)">删除</n-button>
                  </n-space>
                  <n-button
                    type="primary"
                    dashed
                    @click="addCondition"
                    :disabled="!selectedTable || loadingFields"
                  >
                    添加条件
                  </n-button>
                </n-space>
              </n-card>
  
              <n-button
                type="primary"
                block
                @click="executeQuery"
                style="margin-top: 20px;"
                :loading="loadingQuery"
                :disabled="!selectedTable || selectedDisplayFields.length === 0 || loadingFields || loadingQuery"
              >
                执行查询
              </n-button>
            </n-gi>
  
            <!-- Right Panel: Query Results -->
            <n-gi :span="16">
              <n-card title="查询结果">
                <n-data-table
                  :columns="resultTableColumns"
                  :data="queryResults"
                  :loading="loadingQuery"
                  :row-key="row => row.id"
                  v-model:checked-row-keys="checkedRowKeys"
                  :scroll-x="1800"
                />
                <template v-if="!loadingQuery && queryResults.length === 0">
                  <n-empty description="无数据" style="margin-top: 20px;"/>
                </template>
                <n-pagination
                  v-if="pagination.itemCount > 0"
                  v-model:page="pagination.page"
                  v-model:page-size="pagination.pageSize"
                  :item-count="pagination.itemCount"
                  show-size-picker
                  :page-sizes="[10, 20, 50, 100]"
                  style="margin-top: 20px; justify-content: flex-end;"
                  @update:page="handlePageChange"
                  @update:page-size="handlePageSizeChange"
                  :disabled="loadingQuery"
                />
  
                <n-divider v-if="queryResults.length > 0 && !loadingTags" />
  
                <n-space v-if="queryResults.length > 0 && !loadingTags" align="center" style="margin-top: 20px;">
                  <n-select
                    v-model:value="selectedTagForUpdate"
                    :options="tagOptionsForSelect"
                    placeholder="搜索并选择标签"
                    style="min-width: 200px;"
                    filterable
                    clearable
                    :loading="loadingTags"
                  />
                  <n-button
                    type="info"
                    @click="applyTagToSelected"
                    :disabled="checkedRowKeys.length === 0 || !selectedTagForUpdate || loadingApplyingTag"
                    :loading="loadingApplyingTag"
                  >
                    应用选中标签
                  </n-button>
                </n-space>
              </n-card>
            </n-gi>
          </n-grid>
        </n-space>
      </n-config-provider>
    </n-message-provider>
    </CommonPage>
</template>
  
<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import {
NMessageProvider,
NConfigProvider,
NSpace,
NGrid,
NGi,
NCard,
NSelect,
NInput,
NButton,
NDataTable,
NPagination,
NEmpty,
NDivider,
useMessage,
type DataTableColumns,
} from 'naive-ui';
import api from '@/api'

// --- API Interfaces (Conceptual) ---
interface ApiTable {
label: string;
value: string;
}

interface ApiTableField {
label: string;
value: string;
type: 'string' | 'number' | 'date'; // Example types
}

interface ApiQueryCondition {
field: string;
operator: string;
value: any;
}

interface ApiQueryResultItem {
id: number | string; // Must have a unique ID
[key: string]: any;
tag?: string; // Value of the tag
}

interface ApiCategory {
label: string;
value: string;
}

// --- API Placeholder (Simulates backend calls) ---
// In a real app, import this from '~/api' or similar
// const api = {
// getTableList: async (): Promise<ApiTable[]> => {
//     console.log('[API] getTableList called');
//     await new Promise(resolve => setTimeout(resolve, 700));
//     return [
//     { label: 'Brand Reach Data (API)', value: 'brand_reach_api' },
//     { label: 'User Profiles (API)', value: 'user_profiles_api' },
//     { label: 'Product Sales (API)', value: 'product_sales_api' },
//     ];
// },
// getTableFieldsList: async (params: { table_name: string }): Promise<ApiTableField[]> => {
//     console.log('[API] getTableFieldsList called with:', params);
//     await new Promise(resolve => setTimeout(resolve, 600));
//     if (params.table_name === 'brand_reach_api') {
//     return [
//         { label: 'Campaign ID', value: 'campaign_id', type: 'string' },
//         { label: 'Campaign Name', value: 'campaign_name', type: 'string' },
//         { label: 'Impressions', value: 'impressions', type: 'number' },
//         { label: 'Clicks', value: 'clicks', type: 'number' },
//         { label: 'Date', value: 'date', type: 'date' },
//     ];
//     }
//     if (params.table_name === 'user_profiles_api') {
//     return [
//         { label: 'User ID', value: 'user_id', type: 'string' },
//         { label: 'Username', value: 'username', type: 'string' },
//         { label: 'Email', value: 'email', type: 'string' },
//         { label: 'Age', value: 'age', type: 'number' },
//     ];
//     }
//     return [];
// },
// getQueryResultList: async (params: {
//     tableName: string;
//     fieldsToDisplay: string[]; // Backend might use this or just return all relevant fields
//     conditions: ApiQueryCondition[];
//     page: number;
//     pageSize: number;
// }): Promise<{ data: ApiQueryResultItem[]; total: number }> => {
//     console.log('[API] getQueryResultList called with:', params);
//     await new Promise(resolve => setTimeout(resolve, 1200));
    
//     // Mock data generation based on table (backend would do actual filtering & pagination)
//     const allMockData: Record<string, ApiQueryResultItem[]> = {
//         brand_reach_api: Array.from({ length: 125 }, (_, i) => ({
//             id: `br_${i + 1}`,
//             campaign_id: `CAMP${100 + i}`,
//             campaign_name: `Campaign ${String.fromCharCode(65 + (i%26))}-${i}`,
//             impressions: Math.floor(Math.random() * 10000),
//             clicks: Math.floor(Math.random() * 1000),
//             date: new Date(Date.now() - Math.random() * 1e10).toISOString().split('T')[0],
//             tag: i % 10 === 0 ? 'high_value_api' : (i % 7 === 0 ? 'follow_up_api' : undefined)
//         })),
//         user_profiles_api: Array.from({ length: 88 }, (_, i) => ({
//             id: `usr_${i + 1}`,
//             user_id: `USER${200 + i}`,
//             username: `user_${String.fromCharCode(97 + (i%26))}${i}`,
//             email: `user${i}@example.com`,
//             age: 20 + Math.floor(Math.random() * 40),
//             tag: i % 12 === 0 ? 'important_client_api' : undefined
//         })),
//     };
    
//     let sourceData = allMockData[params.tableName] || [];
    
//     // Basic mock filtering (backend would be more sophisticated)
//     if (params.conditions.length > 0) {
//         sourceData = sourceData.filter(item => {
//             return params.conditions.every(cond => {
//                 if (!item.hasOwnProperty(cond.field)) return false;
//                 const itemValue = String(item[cond.field]).toLowerCase();
//                 const condValue = String(cond.value).toLowerCase();
//                 switch(cond.operator) {
//                     case 'eq': return itemValue === condValue;
//                     case 'neq': return itemValue !== condValue;
//                     case 'contains': return itemValue.includes(condValue);
//                     case 'gt': return Number(item[cond.field]) > Number(cond.value);
//                     case 'lt': return Number(item[cond.field]) < Number(cond.value);
//                     default: return true;
//                 }
//             });
//         });
//     }

//     const total = sourceData.length;
//     const start = (params.page - 1) * params.pageSize;
//     const end = start + params.pageSize;
//     return { data: sourceData.slice(start, end), total };
// },
// getCategoryList: async (): Promise<ApiCategory[]> => {
//     console.log('[API] getCategoryList called');
//     await new Promise(resolve => setTimeout(resolve, 400));
//     return [
//     { label: '重要客户 (API)', value: 'important_client_api' },
//     { label: '潜在流失 (API)', value: 'potential_churn_api' },
//     { label: '高价值 (API)', value: 'high_value_api' },
//     { label: '待跟进 (API)', value: 'follow_up_api' },
//     ];
// },
// applyTags: async (params: { itemIds: (string | number)[]; tagValue: string }): Promise<{ success: boolean; message?: string }> => {
//     console.log('[API] applyTags called with:', params);
//     await new Promise(resolve => setTimeout(resolve, 800));
//     // Simulate success
//     return { success: true, message: `Successfully applied tag to ${params.itemIds.length} items.` };
// }
// };
// --- End API Placeholder ---


const message = useMessage();

// --- Loading States ---
const loadingTables = ref(false);
const loadingFields = ref(false);
const loadingQuery = ref(false);
const loadingTags = ref(false);
const loadingApplyingTag = ref(false);

// --- Data Holders ---
const availableTables = ref([]);
const currentTableFields = ref([]);
const availableTags = ref([]);

// Table Selection
const selectedTable = ref<string | null>(null);

// Field Selection (for display)
const selectedDisplayFields = ref<string[]>([]);

// Query Conditions
interface QueryConditionUI extends QueryCondition { id: number; } // For UI key
const queryConditions = ref<QueryConditionUI[]>([]);
let conditionIdCounter = 0;
const availableOperators = ref([
{ label: '等于', value: 'eq' },
{ label: '不等于', value: 'neq' },
{ label: '包含', value: 'contains' },
{ label: '大于', value: 'gt' },
{ label: '小于', value: 'lt' },
]);

// Query Results
const queryResults = ref<ApiQueryResultItem[]>([]); // Holds current page data
const checkedRowKeys = ref<Array<string | number>>([]);
const pagination = reactive({
page: 1,
pageSize: 10,
itemCount: 0,
});

// Tagging
const selectedTagForUpdate = ref<string | null>(null);

// --- Computed Properties for Select Options ---
const tableOptions = computed(() => availableTables.value.map(t => ({ label: t, value: t })));
const fieldOptionsForSelect = computed(() => currentTableFields.value.map(f => ({ label: f, value: f })));
// const tagOptionsForSelect = computed(() => availableTags.value.map(t => ({ label: t.label, value: t.value })));
const tagOptionsForSelect = ref([])

// --- Methods ---
const fetchTables = async () => {
loadingTables.value = true;
try {
    const table_response = await api.getTableList()
    availableTables.value = table_response.data
} catch (err) {
    message.error("加载数据表列表失败");
    console.error("fetchTables error:", err);
} finally {
    loadingTables.value = false;
}
};

const fetchTableFields = async (tableName: string) => {
loadingFields.value = true;
currentTableFields.value = []; // Clear previous
selectedDisplayFields.value = []; // Reset display fields
queryConditions.value = []; // Reset conditions
try {
    const field_response = await api.getTableFieldsList({ table_name: tableName })
    currentTableFields.value = field_response.data
} catch (err) {
    message.error(`加载表 "${tableName}" 字段失败`);
    console.error("fetchTableFields error:", err);
} finally {
    loadingFields.value = false;
}
};

const fetchTags = async () => {
loadingTags.value = true;
try {
    const tag_response = await api.getCategoryList();
    // 递归转换数据结构
    const transformTree = (nodes) => {
      return nodes.map((node) => ({
        label: node.label,
        key: node.id, // 使用 id 作为节点的唯一标识
        children: node.children ? transformTree(node.children) : undefined,
      }))
    }
    tagOptionsForSelect.value = transformTree(tag_response.data)

} catch (err) {
    message.error("加载标签列表失败");
    console.error("fetchTags error:", err);
} finally {
    loadingTags.value = false;
}
};

watch(selectedTable, async (newTable) => {
queryResults.value = [];
pagination.itemCount = 0;
pagination.page = 1;
checkedRowKeys.value = [];

if (newTable) {
    await fetchTableFields(newTable);
} else {
    // Clear fields if table is deselected
    currentTableFields.value = [];
    selectedDisplayFields.value = [];
    queryConditions.value = [];
}
});

const addCondition = () => {
queryConditions.value.push({
    id: conditionIdCounter++,
    field: null,
    operator: 'eq',
    value: '',
});
};

const removeCondition = (index: number) => {
queryConditions.value.splice(index, 1);
};

const executeQuery = async () => {
if (!selectedTable.value || selectedDisplayFields.value.length === 0) {
    message.warning('请先选择数据表和要展示的字段');
    return;
}
loadingQuery.value = true;
checkedRowKeys.value = []; // Clear selection on new query

try {
    const apiConditions: ApiQueryCondition[] = queryConditions.value
    .filter(c => c.field && c.operator && String(c.value).trim() !== '')
    .map(c => ({
        field: c.field!,
        operator: c.operator!,
        value: c.value,
    }));
    const response = await api.getQueryResultList({
    tableName: selectedTable.value!,
    fieldsToDisplay: selectedDisplayFields.value,
    conditions: apiConditions,
    page: pagination.page,
    pageSize: pagination.pageSize,
    });

    queryResults.value = response.data;
    pagination.itemCount = response.total;

    if (response.data.length === 0 && response.total > 0) {
        message.info(`当前页无数据，但总共有 ${response.total} 条结果`);
    } else if (response.total > 0) {
        message.success(`查询成功，共 ${response.total} 条结果 (当前页 ${response.data.length} 条)`);
    } else {
        message.info('查询完成，没有符合条件的数据');
    }

} catch (error) {
    message.error('查询失败，请稍后重试');
    console.error("executeQuery error:", error);
    queryResults.value = [];
    pagination.itemCount = 0;
} finally {
    loadingQuery.value = false;
}
};

// Result Table Columns
const resultTableColumns = computed(() => {
if (selectedDisplayFields.value.length === 0 && !selectedTable.value) return []; // Avoid error if no table/fields

const selectionCol = { type: 'selection' as const, fixed: 'left' as const };

const dataCols = selectedDisplayFields.value.map(fieldKey => {
    const fieldInfo = currentTableFields.value.find(f => f.value === fieldKey);
    return {
    title: fieldInfo ? fieldInfo.label : fieldKey,
    key: fieldKey,
    resizable: true,
    ellipsis: { tooltip: true }
    };
});

const tagCol = {
    title: '标签',
    key: 'tag',
    resizable: true,
    width: 120,
    ellipsis: { tooltip: true },
    render(row: ApiQueryResultItem) {
    if (row.tag) {
        const tagInfo = availableTags.value.find(t => t.value === row.tag);
        return tagInfo ? tagInfo.label : row.tag;
    }
    return '无';
    }
};

// Always show selection and tag columns if there are display fields
// or even if no display fields are selected but a query might run (though query is disabled then)
if (selectedDisplayFields.value.length > 0 || selectedTable.value) {
    return [selectionCol, ...dataCols, tagCol];
}
return [selectionCol, tagCol]; // Minimal columns if no fields selected but table is.
});


const handlePageChange = (page: number) => {
pagination.page = page;
executeQuery();
};

const handlePageSizeChange = (pageSize: number) => {
pagination.pageSize = pageSize;
pagination.page = 1;
executeQuery();
};

const applyTagToSelected = async () => {
if (!selectedTagForUpdate.value || checkedRowKeys.value.length === 0) {
    message.warning('请选择要应用的标签和至少一条数据');
    return;
}
loadingApplyingTag.value = true;
try {
    const result = await api.applyTags({
    itemIds: checkedRowKeys.value,
    tagValue: selectedTagForUpdate.value!,
    });

    if (result.success) {
    message.success(result.message || `成功为 ${checkedRowKeys.value.length} 条数据应用了标签`);
    // Optimistic update of local data
    queryResults.value = queryResults.value.map(row => {
        if (checkedRowKeys.value.includes(row.id)) {
        return { ...row, tag: selectedTagForUpdate.value! };
        }
        return row;
    });
    checkedRowKeys.value = [];
    selectedTagForUpdate.value = null;
    // Optionally, you might want to re-fetch data to ensure consistency:
    // await executeQuery();
    } else {
    message.error(result.message || '应用标签失败');
    }
} catch (err) {
    message.error('应用标签时发生错误');
    console.error("applyTagToSelected error:", err);
} finally {
    loadingApplyingTag.value = false;
}
};

// Fetch initial data on mount
onMounted(async () => {
await fetchTables();
await fetchTags();
});

</script>

<style scoped>
.n-card {
border-radius: 8px;
}
/* Ensure inputs in conditions don't stretch too much if needed */
.n-space > .n-input {
flex-grow: 1;
}
</style>
