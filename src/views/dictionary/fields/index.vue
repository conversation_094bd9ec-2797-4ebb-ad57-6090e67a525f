<script setup>
import { ref, onMounted, h, toRaw } from 'vue'
import {
  NCard,
  NGrid,
  NGi,
  NSpin,
  NStatistic,
  NModal,
  NDataTable,
  NSelect,
  NInput,
  NButton,
} from 'naive-ui'
import api from '@/api'

const loading = ref(true)
const cardData = ref({})
const showModal = ref(false)
const currentKey = ref('')

// 车型
const vehicleOptions = ref([])
const vehicleValue = ref(null)
const vehicleTable = ref([])
// 零件
const partOptions = ref([])
const partValue = ref(null)
// VIO
const vioOptions = ref([])
const vioValue = ref(null)
const vioTable = ref([
  { field: 'vehicle_id', selectVal: null },
  { field: 'vio', selectVal: null },
])

const fetchCardData = async () => {
  loading.value = true
  try {
    const res = await api.getSourceInfo()
    cardData.value = res.data || {}
  } catch (e) {
    cardData.value = {}
  } finally {
    loading.value = false
  }
}

const handleCardClick = async (key) => {
  currentKey.value = key
  showModal.value = true
  await fetchAllDetailData(key)
}

// 获取下拉数据
const fetchSelectOptions = async (param, targetOptions) => {
  try {
    const res = await api.getTableList({ database: param })
    targetOptions.value = (res.data || []).map((item) => ({
      label: item,
      value: item,
    }))
  } catch (e) {
    targetOptions.value = []
  }
}

// 弹窗打开时加载所有下拉框和表格
const fetchAllDetailData = async (key) => {
  // 车型下拉
  await fetchSelectOptions('BeforeProcessing', vehicleOptions)
  vehicleValue.value = null
  // 零件下拉
  await fetchSelectOptions('BeforeProcessing', partOptions)
  partValue.value = null
  // VIO下拉
  await fetchSelectOptions('BeforeProcessing', vioOptions)
  vioValue.value = null

  // 车型表格
  const vehicleFieldsRes = await api.getTableFieldsList({ table_name: 'vehicle_data' })
  const vehicleFields = vehicleFieldsRes.data || []
  vehicleTable.value = vehicleFields.map((f) => ({
    field: f,
    selectVal: null,
    isInit: true, // 初始字段
  }))
  vehicleColumns.value = [
    {
      title: '车型字段',
      key: 'field',
      render(row) {
        if (row.isInit) {
          // 初始字段不可编辑
          return h('span', row.field)
        }
        return h(NInput, {
          value: row.field,
          onUpdateValue: (v) => (row.field = v),
        })
      },
    },
    {
      title: '下拉选择',
      key: 'selectVal',
      render(row) {
        return h(NSelect, {
          options: vehicleSelectOptions.value,
          value: row.selectVal,
          onUpdateValue: (v) => (row.selectVal = v),
          filterable: true,
          clearable: true,
          tag: true, // 允许自定义输入
          allowInput: true, // 允许输入自定义内容（新版本 Naive UI 推荐）
        })
      },
    },
    {
      title: '操作',
      key: 'actions',
      render(row, index) {
        // 只有新增行才显示删除按钮
        if (row.isInit) return null
        return h(
          NButton,
          {
            type: 'error',
            size: 'small',
            onClick: () => vehicleTable.value.splice(index, 1),
          },
          { default: () => '删除' }
        )
      },
    },
  ]

  // 零件表格
  const partFieldsRes = await api.getTableFieldsList({ table_name: 'part_num' })
  const partFields = partFieldsRes.data || []

  // 先加 vehicle_id，再加其它字段（去重）
  const partTableData = [
    { field: 'vehicle_id', selectVal: null, isInit: true },
    ...partFields
      .filter((f) => f !== 'vehicle_id')
      .map((f) => ({ field: f, selectVal: null, isInit: true })),
  ]
  partTable.value = partTableData
  partColumns.value = [
    {
      title: '零件字段',
      key: 'field',
      render(row) {
        if (row.isInit) {
          return h('span', row.field)
        }
        return h(NInput, {
          value: row.field,
          onUpdateValue: (v) => (row.field = v),
        })
      },
    },
    {
      title: '下拉选择',
      key: 'selectVal',
      render(row) {
        return h(NSelect, {
          options: partSelectOptions.value,
          value: row.selectVal,
          onUpdateValue: (v) => (row.selectVal = v),
          filterable: true,
          clearable: true,
          tag: true, // 允许自定义输入
          allowInput: true, // 允许输入自定义内容
        })
      },
    },
    {
      title: '操作',
      key: 'actions',
      render(row, index) {
        if (row.isInit) return null
        return h(
          NButton,
          {
            type: 'error',
            size: 'small',
            onClick: () => partTable.value.splice(index, 1),
          },
          { default: () => '删除' }
        )
      },
    },
  ]
}

// 下拉框切换事件
const handleVehicleChange = async (val) => {
  vehicleValue.value = val
  // 只请求字段列表，作为下拉选项
  const fieldsRes = await api.getTableFieldsList({ database: 'BeforeProcessing', table_name: val })
  const fields = fieldsRes.data || []
  vehicleSelectOptions.value = fields.map((f) => ({ label: f, value: f }))
  // 不刷新 vehicleTable.value，保持第一列内容不变
}
const handlePartChange = async (val) => {
  partValue.value = val
  // 只请求字段列表，作为下拉选项
  const fieldsRes = await api.getTableFieldsList({ database: 'BeforeProcessing', table_name: val })
  const fields = fieldsRes.data || []
  partSelectOptions.value = fields.map((f) => ({ label: f, value: f }))
  // 不刷新 partTable.value，保持第一列内容不变
}
// VIO下拉框切换事件（如需动态获取options，可在此请求接口）
const handleVioChange = async (val) => {
  vioValue.value = val
  // 请求字段列表
  const fieldsRes = await api.getTableFieldsList({ database: 'BeforeProcessing', table_name: val })
  const fields = fieldsRes.data || []
  vioSelectOptions.value = fields.map((f) => ({ label: f, value: f }))
  // 不刷新 vioTable，只更新下拉选项
}

// 表格列（可根据实际接口调整）
const vehicleColumns = ref([]) // 车型表格列
//const vehicleTable = ref([]) // 车型表格数据
const vehicleSelectOptions = ref([]) // 车型表格第二列下拉选项

// 零件表格列和数据
const partColumns = ref([
  {
    title: '零件字段',
    key: 'field',
    render(row) {
      if (row.isInit) {
        return h('span', row.field)
      }
      return h(NInput, {
        value: row.field,
        onUpdateValue: (v) => (row.field = v),
      })
    },
  },
  {
    title: '下拉选择',
    key: 'selectVal',
    render(row) {
      return h(NSelect, {
        options: partSelectOptions.value,
        value: row.selectVal,
        onUpdateValue: (v) => (row.selectVal = v),
        filterable: true,
        clearable: true,
        tag: true, // 允许自定义输入
        allowInput: true, // 允许输入自定义内容
      })
    },
  },
  {
    title: '操作',
    key: 'actions',
    render(row, index) {
      if (row.isInit) return null
      return h(
        NButton,
        {
          type: 'error',
          size: 'small',
          onClick: () => partTable.value.splice(index, 1),
        },
        { default: () => '删除' }
      )
    },
  },
])
const partTable = ref([])
const partSelectOptions = ref([]) // 零件表格第二列下拉选项

const vioColumns = [
  {
    title: '字段名',
    key: 'field',
    render(row) {
      return h('span', row.field)
    },
  },
  {
    title: '下拉选择',
    key: 'selectVal',
    render(row) {
      return h(NSelect, {
        options: vioSelectOptions.value,
        value: row.selectVal,
        onUpdateValue: (v) => (row.selectVal = v),
        filterable: true,
        clearable: true,
        tag: true,
        allowInput: true,
        multiple: row.field === 'VIO', // 只有VIO行多选
        maxTagCount: row.field === 'VIO' ? 4 : undefined,
      })
    },
  },
]

const vioSelectOptions = ref([]) // VIO区域下拉选项

const addVehicleRow = () => {
  vehicleTable.value.push({
    field: '',
    selectVal: null,
    isInit: false, // 新增行
  })
}

const addPartRow = () => {
  partTable.value.push({
    field: '',
    selectVal: null,
    isInit: false, // 新增行
  })
}

const handleSubmit = async () => {
  try {
    const jsonData = {
      vehicleValue: vehicleValue.value,
      vehicleTable: reactiveToJson(vehicleTable.value),
      partValue: partValue.value,
      partTable: reactiveToJson(partTable.value),
      vioValue: vioValue.value,
      vioTable: reactiveToJson(vioTable.value),
    }
    // 将 jsonData 转换为字符串，格式化输出
    const jsonString = JSON.stringify(jsonData, null, 2)

    const response = await api.updateSourceData({
      data: jsonString,
    })
    console.log(response)
  } catch (error) {
    console.error('Error updating data:', error)
  }
}

function reactiveToJson(data, space = 2) {
  const rawData = toRaw(data)

  // 检查是否为表格数据数组，如果是则添加 isSelectval 参数
  if (Array.isArray(rawData)) {
    const processedData = rawData.map((item) => {
      // 检查是否有 selectVal 字段且为空或null
      if (
        item.hasOwnProperty('selectVal') &&
        (item.selectVal === null ||
          item.selectVal === undefined ||
          item.selectVal === '' ||
          (Array.isArray(item.selectVal) && item.selectVal.length === 0))
      ) {
        return {
          ...item,
          isSelectval: false, // 第二列没有选中值
        }
      }
      return {
        ...item,
        isSelectval: true, // 第二列有选中值
      }
    })

    return JSON.parse(
      JSON.stringify(
        processedData,
        (k, v) => v ?? null // 处理 undefined 值
      ),
      space
    )
  }

  return JSON.parse(
    JSON.stringify(
      rawData,
      (k, v) => v ?? null // 处理 undefined 值
    ),
    space
  )
}

onMounted(fetchCardData)
</script>

<template>
  <n-spin :show="loading" description="数据加载中...">
    <n-grid :cols="4" x-gap="32" y-gap="32" class="card-grid">
      <n-gi v-for="(item, key) in cardData" :key="key">
        <n-card hoverable class="data-card" @click="handleCardClick(key)">
          <template #header>
            <span class="card-title">{{ key }} 数据</span>
          </template>
          <div class="card-content">
            <n-statistic label="车型总量" :value="item.count" />
            <n-statistic label="零件总量" :value="item.part" />
            <n-statistic label="VIO" :value="item.vio" />
          </div>
        </n-card>
      </n-gi>
    </n-grid>
    <!-- 弹窗 -->
    <n-modal
      v-model:show="showModal"
      preset="card"
      :title="currentKey"
      style="width: 90vw; max-width: 1300px; height: 90vh; max-height: 90vh; overflow: hidden"
      :content-style="{ height: '100%', overflow: 'auto', padding: '0' }"
    >
      <div class="modal-container" style="height: 100%; overflow: auto">
        <!-- 车型数据 -->
        <div class="modal-section">
          <h3>车型数据</h3>
          <div class="modal-select-row" style="display: flex; gap: 12px; align-items: center">
            <n-select
              v-model:value="vehicleValue"
              :options="vehicleOptions"
              placeholder="请选择车型表"
              style="flex: 1"
              clearable
              @update:value="handleVehicleChange"
            />
            <n-button type="primary" @click="addVehicleRow">新增行</n-button>
          </div>
          <n-data-table
            :columns="vehicleColumns"
            :data="vehicleTable"
            :bordered="false"
            :scroll-x="600"
            :max-height="260"
            :fixed-header="true"
            style="overflow-y: auto"
          />
        </div>
        <!-- 零件数据 -->
        <div class="modal-section">
          <h3>零件数据</h3>
          <div class="modal-select-row" style="display: flex; gap: 12px; align-items: center">
            <n-select
              v-model:value="partValue"
              :options="partOptions"
              placeholder="请选择零件表"
              clearable
              @update:value="handlePartChange"
            />
            <n-button type="primary" @click="addPartRow">新增行</n-button>
          </div>
          <n-data-table
            :columns="partColumns"
            :data="partTable"
            :bordered="false"
            :scroll-x="600"
            :max-height="260"
            :fixed-header="true"
            style="overflow-y: auto"
          />
        </div>
        <!-- VIO数据 -->
        <div class="modal-section">
          <h3>VIO数据</h3>
          <div class="modal-select-row">
            <n-select
              v-model:value="vioValue"
              :options="vioOptions"
              placeholder="请选择VIO表"
              clearable
              @update:value="handleVioChange"
            />
          </div>
          <n-data-table
            :columns="vioColumns"
            :data="vioTable"
            :bordered="false"
            :scroll-x="400"
            :max-height="120"
            :fixed-header="true"
            style="overflow-y: auto"
          />
        </div>
        <n-button
          type="primary"
          style="
            margin: 24px auto 0;
            display: block;
            width: 80%;
            text-align: center;
            justify-content: center;
            align-items: center;
          "
          @click="handleSubmit"
        >
          <span style="display: inline-block; width: 100%; text-align: center">提交</span>
        </n-button>
      </div>
    </n-modal>
  </n-spin>
</template>

<style scoped>
.card-grid {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  margin-top: 50px;
}
.data-card {
  min-width: 220px;
  max-width: 320px;
  min-height: 180px;
  margin: 0 auto;
  border-radius: 18px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.25s, transform 0.25s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #fff;
}
.data-card:hover {
  box-shadow: 0 12px 32px rgba(25, 118, 210, 0.18);
  transform: translateY(-6px) scale(1.04);
  z-index: 2;
}
.card-title {
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 2px;
  text-align: center;
}
.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 12px;
}
.modal-container {
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 12px 0;
}
.modal-section {
  background: #fafbfc;
  border-radius: 10px;
  padding: 18px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.modal-select-row {
  width: 100%;
  margin-bottom: 12px;
  display: block;
}
.modal-select-row :deep(.n-select) {
  width: 100%;
}
</style>
