<template>
  <n-config-provider :theme="theme" style="padding-top: 20px">
    <div class="scrollable-container">
      <!-- 顶部导航栏 - 优化为更现代化的设计 -->
      <header class="sticky top-0 z-50 bg-white shadow-sm transition-all duration-300">
        <div class="mx-auto px-4 container">
          <div class="h-16 flex items-center justify-between">
            <!-- 角色选择器 - 改为选项卡样式 -->
            <n-tabs
              v-model:value="currentRole"
              type="line"
              animated
              class="hidden rounded-lg bg-gray-100 px-1 py-1 md:flex"
              size="medium"
            >
              <n-tab v-if="iscustomer || isadmin" name="customer">客户</n-tab>
              <n-tab v-if="issales || isadmin" name="sales">销售</n-tab>
              <n-tab v-if="issupport || isadmin" name="support">客服</n-tab>
            </n-tabs>
          </div>
        </div>
      </header>

      <!-- 主内容区 -->
      <main class="mx-auto flex-grow px-4 py-6 container">
        <!-- 搜索和功能区 - 优化布局和阴影 -->
        <n-card class="mb-6 rounded-xl shadow-md" :bordered="false">
          <div class="mb-6 flex flex-col justify-between gap-4 md:flex-row md:items-center">
            <div>
              <h2 class="font-bold text-gray-800">配件询报价</h2>
              <p class="mt-1 text-gray-500">查找并管理您的汽车配件询价</p>
            </div>
            <div class="flex flex-wrap gap-2">
              <!-- <n-button type="primary" class="gap-2" @click="createInquiry">
                <template #icon>
                  <n-icon>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                  </n-icon>
                </template>
                新建询价
              </n-button> -->
              <n-button
                v-if="iscustomer || isadmin || issupport"
                type="warning"
                class="gap-2"
                @click="openBatchModal"
              >
                <template #icon>
                  <n-icon>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="3" y1="9" x2="21" y2="9"></line>
                      <line x1="9" y1="21" x2="9" y2="9"></line>
                    </svg>
                  </n-icon>
                </template>
                批量询价
              </n-button>
              <n-button class="gap-2">
                <template #icon>
                  <n-icon>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                  </n-icon>
                </template>
                历史记录
              </n-button>
            </div>
          </div>

          <!-- 高级搜索 - 优化间距和标签 -->
          <div class="grid grid-cols-1 mb-4 gap-3 md:grid-cols-5">
            <div class="relative">
              <n-input v-model:value="inputNumber" placeholder="搜索配件编号">
                <template #prefix>
                  <n-icon>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <circle cx="11" cy="11" r="8"></circle>
                      <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                  </n-icon>
                </template>
              </n-input>
            </div>

            <n-select
              v-model:value="selectMarket"
              placeholder="市场区域"
              :options="marketRegions"
            />

            <n-select v-model:value="selectPart" placeholder="配件类别" :options="partCategories" />

            <!-- <n-select placeholder="适配车型" :options="carModels" /> -->

            <n-button type="info" class="w-full" @click="handleSearch">
              <template #icon>
                <n-icon>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <circle cx="11" cy="11" r="8" />
                    <line x1="21" y1="21" x2="16.65" y2="16.65" />
                  </svg>
                </n-icon>
              </template>
              搜索
            </n-button>
          </div>
        </n-card>

        <!-- 主要内容区 -->
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-3" style="padding-top: 10px">
          <!-- 左侧：产品列表 -->
          <div class="lg:col-span-2">
            <!-- 产品列表标题 -->
            <div class="mb-4 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h3 class=" font-bold text-gray-800">配件产品列表</h3>
                <p class="text-gray-500">浏览并选择您需要的配件</p>
              </div>
              <div class="flex items-center gap-2">
                <span class="text-gray-500">
                  共找到 <span class="font-medium text-primary">{{ totalItems }}</span> 个产品
                </span>
                <!-- <n-select
                  :options="sortOptions"
                  size="small"
                  style="width: 140px"
                  v-model:value="sortOption"
                /> -->
              </div>
            </div>

            <!-- 卡片视图 -->
            <div v-if="viewMode === 'card'" class="grid grid-cols-1 gap-4 md:grid-cols-2">
              <n-card
                v-for="product in products"
                :key="product.id"
                hoverable
                class="transition-all duration-300 hover:shadow-lg"
                :title="product.name"
                size="small"
              >
                <template #cover>
                  <div class="relative">
                    <img
                      :src="product.image"
                      :alt="product.name"
                      class="h-48 w-full object-cover"
                    />
                    <n-tag
                      v-if="product.tag"
                      :type="tagTypeMap[product.tag]"
                      size="small"
                      class="absolute left-2 top-2"
                    >
                      {{ product.tag }}
                    </n-tag>
                    <!-- <n-tag type="info" size="small" class="absolute top-2 right-2">
                      <template #icon>
                        <n-icon>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          >
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="2" y1="12" x2="22" y2="12"></line>
                            <path
                              d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"
                            ></path>
                          </svg>
                        </n-icon>
                      </template>
                      {{ product.region }}
                    </n-tag> -->
                  </div>
                </template>

                <div class="py-2">
                  <p class="mb-3 text-gray-600">{{ product.description }}</p>

                  <div class="mb-4 flex items-center justify-between">
                    <div class="space-y-1">
                      <div
                        v-for="(feature, idx) in product.features"
                        :key="idx"
                        class="flex items-center text-xs text-gray-500"
                      >
                        <n-icon class="mr-1 text-green-500">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          >
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22 4 12 14.01 9 11.01"></polyline>
                          </svg>
                        </n-icon>
                        <!-- <span>{{ feature }}</span> -->
                      </div>
                    </div>
                    <div v-if="showSalesFeatures" class="text-danger  font-bold">
                      ¥{{ product.price }}
                    </div>
                  </div>

                  <n-button type="primary" class="w-full" @click="addToQuote(product)">
                    <template #icon>
                      <n-icon>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <circle cx="10" cy="20.5" r="1"></circle>
                          <circle cx="18" cy="20.5" r="1"></circle>
                          <path
                            d="M2.5 2.5h3l2.7 12.4a2 2 0 0 0 2 1.6h7.7a2 2 0 0 0 2-1.6l1.6-8.4H7.1"
                          ></path>
                          <path d="M9 10l4-1 4 1"></path>
                        </svg>
                      </n-icon>
                    </template>
                    加入询价
                  </n-button>
                </div>
              </n-card>
            </div>

            <!-- 列表视图 -->
            <div
              v-else-if="viewMode === 'list'"
              class="h-[4,00px] overflow-auto border border-gray-200 rounded-lg"
            >
              <n-table :bordered="true" class="rounded-lg bg-white shadow-sm">
                <thead>
                  <tr class="bg-gray-50">
                    <th class="p-3 text-left">序号</th>
                    <th class="p-3 text-left">配件名称</th>
                    <th class="p-3 text-left">配件编码</th>
                    <th class="p-3 text-left">OEM</th>
                    <th class="p-3 text-left">竞品品牌</th>
                    <th class="p-3 text-left">竞品编码</th>
                    <th class="p-3 text-left">适配结果</th>
                    <!-- <th class="text-left p-3">适配描述</th> -->
                    <th class="p-3 text-left">价格</th>
                    <!-- <th class="text-left p-3">适配车型</th> -->
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(product, index) in batchProducts"
                    :key="product.id"
                    class="transition-colors hover:bg-gray-50"
                  >
                    <td class="p-3">{{ product.id }}</td>
                    <td class="p-3 font-medium">{{ product.name }}</td>
                    <td class="p-3 font-medium">{{ product.code }}</td>
                    <td class="p-3 text-gray-500">{{ product.oem }}</td>
                    <td class="p-3">{{ product.competitorBrand }}</td>
                    <td class="p-3">{{ product.competitorCode }}</td>
                    <td class="p-3">{{ product.compatibility }}</td>
                    <td class="text-danger p-3 font-bold">
                      {{ product.price }}
                    </td>
                    <!-- <td class="p-3">
                      <n-button size="small" type="primary" @click="addToQuote(product)"
                        >加入询价</n-button
                      >
                    </td> -->
                  </tr>
                </tbody>
              </n-table>
            </div>

            <!-- 分页 -->
            <div v-if="totalPages > 0 && viewMode === 'card'" class="mt-6 flex justify-center">
              <n-pagination
                v-model:page="currentPage"
                :page-count="totalPages"
                :page-size="pageSize"
              />
            </div>
          </div>

          <!-- 右侧：询价单/报价单 - 优化卡片设计 -->
          <div class="lg:col-span-1">
            <n-card class="sticky top-24 rounded-xl shadow-md" :bordered="false">
              <div class="mb-4 flex items-center justify-between">
                <h3 class=" font-bold text-gray-800">当前询价单</h3>
                <n-tag type="primary" round>{{ quoteItems.length }} 个配件</n-tag>
              </div>

              <!-- 询价市场区域选择 -->
              <div v-if="quoteItems.length > 0" class="mb-4">
                <n-form-item label="目标市场区域" required>
                  <n-select
                    v-model:value="selectedMarketRegion"
                    placeholder="请选择市场区域"
                    :options="marketRegions"
                  />
                </n-form-item>
              </div>

              <!-- 询价单为空状态 -->
              <div v-if="quoteItems.length === 0 && viewMode != 'list'" class="py-10 text-center">
                <n-empty description="您的询价单还是空的">
                  <template #icon>
                    <n-icon size="40" class="text-gray-300">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="40"
                        height="40"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <circle cx="9" cy="21" r="1"></circle>
                        <circle cx="20" cy="21" r="1"></circle>
                        <path
                          d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"
                        ></path>
                      </svg>
                    </n-icon>
                  </template>
                  <template #extra>
                    <p class="mt-2 text-gray-500">浏览产品并点击"加入询价"添加配件</p>
                  </template>
                </n-empty>
              </div>

              <!-- 询价单列表 -->
              <div v-else class="mb-4 max-h-64 overflow-y-auto pr-2 space-y-3">
                <div
                  v-for="(item, idx) in quoteItems"
                  :key="idx"
                  class="flex items-center rounded-lg bg-gray-50 p-3"
                >
                  <n-avatar :src="item.image" round size="medium" class="mr-3" />
                  <div class="min-w-0 flex-grow">
                    <h4 class="truncate font-medium">{{ item.name }}</h4>
                    <p class="truncate text-gray-500">{{ item.description }}</p>
                    <!-- <p class="text-xs text-danger font-medium mt-1">¥{{ item.price }}</p> -->
                  </div>
                  <n-input-number
                    v-model:value="item.quantity"
                    :min="1"
                    size="small"
                    class="w-20"
                    style="width: 20rem"
                  />
                  <n-button type="error" text class="ml-2" @click="removeItem(idx)">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M3 6h18M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2M10 11v6M14 11v6"
                      />
                    </svg>
                  </n-button>
                </div>
              </div>

              <!-- 询价单备注 -->
              <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <n-form-item label="客户名称" required>
                  <n-input v-model:value="batchCustomerName" placeholder="请输入客户名称" />
                </n-form-item>
                <n-form-item label="联系电话" required>
                  <n-input v-model:value="batchCustomerPhone" placeholder="请输入联系电话" />
                </n-form-item>
                <n-form-item label="截止日期" required>
                  <n-date-picker
                    v-model:value="batchDeadline"
                    type="date"
                    placeholder="请选择截止日期"
                    clearable
                    :is-date-disabled="disablePreviousDates"
                  />
                </n-form-item>
                <n-form-item label="任务等级" required>
                  <n-select
                    v-model:value="batchTaskLevel"
                    placeholder="请选择任务等级"
                    :options="taskLevelOptions"
                    clearable
                  />
                </n-form-item>
                <n-form-item label="询价备注" class="md:col-span-2">
                  <n-input
                    v-model:value="batchNotes"
                    type="textarea"
                    placeholder="请输入特殊要求或其他说明..."
                    :rows="3"
                  />
                </n-form-item>
              </div>

              <!-- 询价单总计 -->
              <!-- <div class="border-t border-gray-200 pt-4 mb-4" v-if="quoteItems.length > 0">
                <div class="flex justify-between mb-2">
                  <span class=" text-gray-600">小计</span>
                  <span class=" font-medium">¥{{ subtotal }}</span>
                </div>
                <div class="flex justify-between mb-2">
                  <span class=" text-gray-600">预计运费</span>
                  <span class=" font-medium">¥20.00</span>
                </div>
                <div class="flex justify-between font-bold pt-2 border-t border-gray-200 mt-2">
                  <span class="text-gray-700">总计</span>
                  <span class="text-danger ">¥{{ total }}</span>
                </div>
              </div> -->

              <!-- 提交询价按钮 -->
              <n-button
                type="primary"
                class="w-full"
                :disabled="quoteItems.length === 0"
                size="large"
                @click="submitQuote"
              >
                提交询价
              </n-button>
            </n-card>
          </div>
        </div>
      </main>

      <!-- 询价成功弹窗 -->
      <n-modal
        v-model:show="showSuccessModal"
        preset="card"
        style="max-width: 520px"
        title="询价提交成功"
      >
        <div class="py-4 text-center">
          <div
            class="mx-auto mb-4 h-16 w-16 flex items-center justify-center rounded-full bg-success/10"
          >
            <n-icon size="32" class="text-success">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </n-icon>
          </div>

          <p class="mb-6 text-gray-600">您的询价单已提交，我们将尽快与您联系</p>

          <n-card class="mb-6 bg-gray-50">
            <div class="text-left space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-500">询价单号:</span>
                <span class="font-medium">XP202305160024</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">市场区域:</span>
                <span class="font-medium">国内市场</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">预计回复时间:</span>
                <span>2023-05-17 12:00前</span>
              </div>
            </div>
          </n-card>

          <div class="flex gap-3">
            <n-button class="flex-1" @click="showSuccessModal = false">查看历史</n-button>
            <n-button type="primary" class="flex-1" @click="showSuccessModal = false"
              >继续浏览</n-button
            >
          </div>
        </div>
      </n-modal>

      <!-- 搜索无结果对话框 -->
      <n-modal
        v-model:show="showNoResultModal"
        preset="card"
        style="width: 90%; max-width: 800px"
        title="联系客服"
        :bordered="false"
      >
        <div class="h-full flex flex-col" style="height: 70vh">
          <!-- 聊天区域 -->
          <div class="flex flex-grow">
            <!-- 左侧联系人列表 -->
            <div class="w-1/4 overflow-y-auto border-r border-gray-200">
              <div class="border-b bg-gray-50 p-3">
                <n-input placeholder="搜索客服" clearable>
                  <template #prefix>
                    <n-icon>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                      </svg>
                    </n-icon>
                  </template>
                </n-input>
              </div>

              <div class="divide-y">
                <!-- 客服联系人项 -->
                <div
                  v-for="(contact, index) in customerServiceContacts"
                  :key="index"
                  class="flex cursor-pointer items-center p-3 hover:bg-gray-50"
                  :class="{ 'bg-blue-50': activeContact === contact.id }"
                  style="height: 70px"
                  @click="activeContact = contact.id"
                >
                  <n-avatar round size="medium" :src="contact.avatar" class="mr-2" />
                  <div class="min-w-0 flex-1">
                    <div class="truncate font-medium">{{ contact.name }}</div>
                    <div
                      class="truncate text-xs text-gray-500"
                      style="padding-top: 10px; padding-bottom: 5px"
                    >
                      {{ contact.lastMessage }}
                    </div>
                  </div>
                  <div class="text-xs text-gray-400">{{ contact.time }}</div>
                </div>
              </div>
            </div>

            <!-- 右侧聊天窗口 -->
            <div class="flex flex-col flex-grow">
              <!-- 聊天头部 -->
              <div class="flex items-center border-b p-3">
                <n-avatar round size="small" :src="activeContactInfo.avatar" class="mr-2" />
                <div class="flex-1">
                  <div class="font-medium">{{ activeContactInfo.name }}</div>
                  <div class="text-xs text-gray-500">在线</div>
                </div>
                <div class="flex space-x-2">
                  <n-button quaternary circle>
                    <template #icon>
                      <n-icon>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <path
                            d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                          ></path>
                        </svg>
                      </n-icon>
                    </template>
                  </n-button>
                  <n-button quaternary circle>
                    <template #icon>
                      <n-icon>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <polygon points="23 7 16 12 23 17 23 7"></polygon>
                          <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
                        </svg>
                      </n-icon>
                    </template>
                  </n-button>
                </div>
              </div>

              <!-- 聊天内容区域 -->
              <div ref="chatContainer" class="flex-grow overflow-y-auto bg-gray-50 p-4">
                <!-- 日期分隔 -->
                <div class="my-4 text-center text-xs text-gray-500">今天</div>

                <!-- 客服消息 -->
                <div class="mb-4 flex">
                  <n-avatar round size="small" :src="activeContactInfo.avatar" class="mr-2" />
                  <div>
                    <div class="rounded-lg rounded-tl-none bg-white p-3 shadow-sm">
                      <p>
                        您好！我是{{ activeContactInfo.name }}，您正在咨询配件编码：<span
                          class="font-medium"
                          >{{ inquiryRequestNumber }}</span
                        >
                      </p>
                      <p class="mt-2">请问有什么可以帮您？</p>
                    </div>
                    <div class="mt-1 text-gray-500">10:05</div>
                  </div>
                </div>

                <!-- 用户消息 -->
                <div class="mb-4 flex justify-end">
                  <div class="text-right">
                    <div class="rounded-lg rounded-tr-none bg-blue-500 p-3 text-white shadow-sm">
                      我想了解这个配件的价格和库存情况
                    </div>
                    <div class="mt-1 text-gray-500">10:08</div>
                    <!-- text-xs  -->
                  </div>
                  <n-avatar
                    round
                    size="small"
                    src="https://avatars.githubusercontent.com/u/18677354?v=4"
                    class="ml-2"
                  />
                </div>

                <!-- 客服回复 -->
                <div class="mb-4 flex">
                  <n-avatar
                    round
                    size="small"
                    :src="activeContactInfo.avatar"
                    class="mr-2"
                  /><!-- max-w-xs -->
                  <div>
                    <div class="rounded-lg rounded-tl-none bg-white p-3 shadow-sm">
                      <p>好的，请稍等，我为您查询...</p>
                    </div>
                    <div class="mt-1 text-gray-500">10:10</div>
                  </div>
                </div>

                <!-- 产品卡片 -->
                <div class="mb-4 flex">
                  <n-avatar round size="small" :src="activeContactInfo.avatar" class="mr-2" />
                  <div>
                    <n-card class="rounded-lg rounded-tl-none" content-style="padding: 0">
                      <div class="flex">
                        <img
                          src="http://*************:85/img/product/Condenser.jpg"
                          alt="配件图片"
                          class="h-70 w-150 object-cover"
                        />
                        <div class="p-3">
                          <div class="font-medium">Reach高端散热器</div>
                          <div class="text-danger mt-1 font-bold">¥100</div>
                          <!-- <div class="text-gray-500 mt-1">库存充足</div> -->
                        </div>
                      </div>
                      <template #action>
                        <n-button type="primary" size="small" class="w-full"> 查看详情 </n-button>
                      </template>
                    </n-card>
                    <div class="mt-1 text-gray-500">10:12</div>
                  </div>
                </div>
              </div>

              <!-- 消息输入区域 -->
              <div class="border-t p-3">
                <div class="flex items-center">
                  <n-button quaternary circle class="mr-1">
                    <template #icon>
                      <n-icon>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <line x1="12" y1="5" x2="12" y2="19"></line>
                          <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                      </n-icon>
                    </template>
                  </n-button>
                  <n-input
                    v-model:value="messageInput"
                    placeholder="输入消息..."
                    type="textarea"
                    :autosize="{ minRows: 1, maxRows: 4 }"
                    class="mr-2 flex-grow"
                    @keyup.enter="sendMessage"
                  />
                  <n-button type="primary" circle @click="sendMessage">
                    <template #icon>
                      <n-icon>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <line x1="22" y1="2" x2="11" y2="13"></line>
                          <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                        </svg>
                      </n-icon>
                    </template>
                  </n-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部功能区 -->
          <div class="flex items-center border-t p-3">
            <div class="text-gray-600">
              您正在咨询配件: <span class="font-medium">{{ inquiryRequestNumber }}</span>
            </div>
            <div class="flex-grow"></div>
            <n-button class="mr-2" @click="showNoResultModal = false">取消</n-button>
            <n-button type="primary" @click="submitInquiryRequest">提交询价请求</n-button>
          </div>
        </div>
      </n-modal>

      <!-- 询价请求提交成功提示 -->
      <n-modal
        v-model:show="showRequestSubmitted"
        preset="card"
        style="max-width: 520px"
        title="询价请求已提交"
        :bordered="false"
      >
        <div class="py-4 text-center">
          <div
            class="mx-auto mb-4 h-16 w-16 flex items-center justify-center rounded-full bg-success/10"
          >
            <n-icon size="32" class="text-success">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </n-icon>
          </div>

          <p class="mb-6 text-gray-600">您的询价请求已提交，客服人员将在24小时内联系您</p>

          <n-card class="mb-6 bg-gray-50">
            <div class="text-left space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-500">配件编码:</span>
                <span class="font-medium">{{ inquiryRequestNumber }}</span>
              </div>
              <!-- <div class="flex justify-between">
                <span class="text-gray-500">适配车型:</span>
                <span class="font-medium">{{ getCarModelLabel(inquiryCarModel) }}</span>
              </div> -->
              <div class="flex justify-between">
                <span class="text-gray-500">市场区域:</span>
                <span class="font-medium">{{ getMarketRegionLabel(inquiryMarketRegion) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">提交时间:</span>
                <span>{{ new Date().toLocaleString() }}</span>
              </div>
            </div>
          </n-card>

          <div class="flex justify-center">
            <n-button type="primary" @click="closeRequestSuccess">确定</n-button>
          </div>
        </div>
      </n-modal>

      <!-- 批量询价弹窗 -->
      <n-modal
        v-model:show="batchModalVisible"
        preset="card"
        style="max-width: 1000px"
        title="批量配件询价"
        :bordered="false"
      >
        <div class="space-y-6">
          <p class="text-gray-600">
            请填写需要询价的配件信息，可通过多种方式查询，支持批量添加多个配件
          </p>

          <!-- <n-form-item label="目标市场区域" required>
            <n-select :options="marketRegions" v-model:value="batchMarketRegion" />
          </n-form-item> -->
          <!-- 新增的文件上传功能 -->
          <n-form-item label="批量导入配件列表">
            <div class="flex items-center gap-2">
              <n-upload
                accept=".xlsx, .xls, .csv"
                :show-file-list="false"
                @change="handleFileUpload"
              >
                <n-button>
                  <template #icon>
                    <n-icon>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                      </svg>
                    </n-icon>
                  </template>
                  上传Excel/CSV文件
                </n-button>
              </n-upload>
              <!-- 添加清除按钮 -->
              <n-button
                type="warning"
                secondary
                :disabled="batchItems.length === 0"
                @click="resetBatchItems"
              >
                <template #icon>
                  <n-icon>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <polyline points="3 6 5 6 21 6"></polyline>
                      <path
                        d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
                      ></path>
                    </svg>
                  </n-icon>
                </template>
                清除数据
              </n-button>
              <!-- <span class="text-gray-500 ">
                支持Excel/CSV格式，表头需包含：配件编码、竞品品牌、竞品编码、适配车型
              </span> -->
            </div>
          </n-form-item>
          <div class="h-[400px] overflow-auto border border-gray-200 rounded-lg">
            <n-table :bordered="false" class="border border-gray-200 rounded-lg">
              <thead>
                <tr class="bg-gray-50">
                  <th class="p-3 text-left">序号</th>
                  <th class="p-3 text-left">配件编码</th>
                  <th class="p-3 text-left">OEM</th>
                  <th class="p-3 text-left">竞品品牌</th>
                  <th class="p-3 text-left">竞品编码</th>
                  <!-- <th class="text-left p-3">适配车型</th> -->
                  <!-- <th class="text-left p-3">图片/图纸</th> -->
                  <th class="w-10 p-3 text-left"></th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in batchItems" :key="index">
                  <td class="p-3">{{ index + 1 }}</td>
                  <td class="p-3">
                    <n-input v-model:value="item.partCode" placeholder="输入配件编码" />
                  </td>
                  <td class="p-3">
                    <n-input v-model:value="item.oem" placeholder="输入OEM" />
                  </td>
                  <td class="p-3">
                    <n-input v-model:value="item.competitorCode" placeholder="输入竞品品牌" />
                  </td>
                  <td class="p-3">
                    <n-input v-model:value="item.brand" placeholder="输入竞品编码" />
                  </td>
                  <!-- <td class="p-3">
                    <n-input v-model:value="item.carModel" placeholder="输入车型" />
                  </td> -->
                  <!-- <td class="p-3">
                      <n-upload action="https://www.mocky.io/v2/5e4bafc63100007100d8b70f">
                        <n-button>
                          <template #icon>
                            <n-icon>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              >
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="17 8 12 3 7 8"></polyline>
                                <line x1="12" y1="3" x2="12" y2="15"></line>
                              </svg>
                            </n-icon>
                          </template>
                          上传图片
                        </n-button>
                      </n-upload>
                    </td> -->
                  <td class="p-3">
                    <n-button
                      text
                      type="error"
                      :disabled="batchItems.length <= 1"
                      @click="removeBatchItem(index)"
                    >
                      <n-icon>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <polyline points="3 6 5 6 21 6"></polyline>
                          <path
                            d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
                          ></path>
                          <line x1="10" y1="11" x2="10" y2="17"></line>
                          <line x1="14" y1="11" x2="14" y2="17"></line>
                        </svg>
                      </n-icon>
                    </n-button>
                  </td>
                </tr>
              </tbody>
            </n-table>
          </div>

          <n-button type="primary" dashed class="w-full" @click="addBatchItem">
            <template #icon>
              <n-icon>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </n-icon>
            </template>
            添加更多配件
          </n-button>

          <!-- <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <n-form-item label="客户名称" required>
              <n-input v-model:value="batchCustomerName" placeholder="请输入客户名称" />
            </n-form-item>
            <n-form-item label="联系电话" required>
              <n-input v-model:value="batchCustomerPhone" placeholder="请输入联系电话" />
            </n-form-item>
            <n-form-item label="询价备注" class="md:col-span-2">
              <n-input
                type="textarea"
                placeholder="请输入特殊要求或其他说明..."
                v-model:value="batchNotes"
                :rows="3"
              />
            </n-form-item>
          </div> -->

          <div class="flex justify-end gap-3">
            <n-button @click="batchModalVisible = false">取消</n-button>
            <n-button type="primary" @click="submitBatchInquiry">批量询价搜索</n-button>
          </div>
        </div>
      </n-modal>
    </div>
  </n-config-provider>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import {
  NConfigProvider,
  NButton,
  NCard,
  NSelect,
  NInput,
  NInputNumber,
  NModal,
  NPagination,
  NAvatar,
  NFormItem,
  NTag,
  NIcon,
  NTabs,
  NTab,
  NTable,
  NUpload,
  NEmpty,
  useMessage,
  useDialog,
} from 'naive-ui'
import { read, utils } from 'xlsx'
import Chart from 'chart.js/auto'
import api from '@/api'

export default defineComponent({
  components: {},
  setup() {
    const message = useMessage()
    const dialog = useDialog()
    // 主题设置
    const theme = ref(null)

    // 角色相关

    const currentRole = ref('customer')

    const showSalesFeatures = computed(() => {
      return currentRole.value === 'sales' || currentRole.value === 'support'
    })
    // 数据选项
    const partCategories = ref([])
    const selectPart = ref(null)
    const selectMarket = ref(null)
    const marketRegions = ref([])

    const inputNumber = ref('')
    // 产品数据
    const products = ref([])
    const loading = ref(false)
    // 询价单相关
    const isadmin = ref(false)
    const iscustomer = ref(false)
    const issales = ref(false)
    const issupport = ref(false)

    const batchProducts = ref([])
    const batchDeadline = ref(null)
    const batchTaskLevel = ref(null)
    const quoteItems = ref([])
    const quoteNotes = ref('')
    const selectedMarketRegion = ref(null)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const totalItems = ref(0)
    const sortOption = ref('default')
    const showSuccessModal = ref(false)
    const searchNumber = ref('')
    const viewMode = ref('')

    const showNoResultModal = ref(false)
    const showRequestSubmitted = ref(false)
    const inquiryRequestNumber = ref('')
    const inquiryCarModel = ref(null)

    const inquiryMarketRegion = ref(null)
    const inquiryNotes = ref('')
    const activeContact = ref('cs1')
    const messageInput = ref('')
    const chatContainer = ref(null)

    const resetBatchItems = () => {
      dialog.warning({
        title: '确认清除',
        content: '此操作将清除所有已导入的数据，是否继续？',
        positiveText: '清除',
        negativeText: '取消',
        onPositiveClick: () => {
          batchItems.value = [
            {
              partCode: '',
              oem: '',
              competitorCode: '',
              brand: '',
              //carModel: '',
            },
          ]
          message.info('已清除所有导入数据')
        },
      })
    }

    // 文件读取方法
    const readUploadedFile = async (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (e) => resolve(e.target.result)
        reader.onerror = reject
        reader.readAsArrayBuffer(file) // 使用现代方法
      })
    }

    // 文件上传处理
    const handleFileUpload = async ({ file }) => {
      if (!file || !file.file) return

      try {
        // 1. 读取文件
        const arrayBuffer = await readUploadedFile(file.file)
        // 2. 转换为 Uint8Array
        const data = new Uint8Array(arrayBuffer)
        // 3. 使用 XLSX 解析
        const workbook = read(data, { type: 'array' })
        // 4. 获取第一个工作表
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]]
        // 5. 转换为 JSON
        const jsonData = utils.sheet_to_json(firstSheet)
        if (!jsonData || jsonData.length === 0) {
          message.warning('文件中未找到有效数据')
          return
        }
        // 表头检测
        const headers = Object.keys(jsonData[0] || {})
        const requiredHeaders = [
          '配件编码',
          'partCode',
          'OEM',
          'oem',
          '竞品品牌',
          'competitorBrand',
          '竞品编码',
          'competitorCode',
          // '适配车型',
          // 'carModel',
        ]
        const missingHeaders = requiredHeaders.filter((h) => !headers.includes(h))

        if (missingHeaders.length === requiredHeaders.length) {
          message.error('文件缺少所有必要表头字段')
          return
        }
        // 6. 映射数据
        const newItems = jsonData.map((row) => ({
          partCode: row['配件编码'] || row['partCode'] || '',
          oem: row['OEM'] || row['oem'] || '',
          competitorCode: row['竞品品牌'] || row['competitorBrand'] || '',
          brand: row['竞品编码'] || row['competitorCode'] || '',
          // carModel: row['适配车型'] || row['carModel'] || '',
        }))
        // 7. 更新批量项目
        batchItems.value = [...batchItems.value, ...newItems]

        // 8.清除空行
        if (batchItems.value.length === 0) return

        // 过滤掉所有字段都为空的项
        const nonEmptyItems = batchItems.value.filter(
          (item) => item.partCode || item.oem || item.competitorCode || item.brand // || item.carModel
        )

        // 如果过滤后为空，则保留一个空行
        batchItems.value = nonEmptyItems.length > 0 ? nonEmptyItems : [createEmptyBatchItem()]
        message.success(`成功导入 ${newItems.length} 条配件信息`)
      } catch (error) {
        console.error('文件导入失败', error)
        message.error('文件导入失败，请检查文件格式')
      }
    }
    // 获取车型标签
    // const getCarModelLabel = (value) => {
    //   const model = carModels.find((item) => item.value === value)
    //   return model ? model.label : '未选择'
    // }

    // 获取市场区域标签
    const getMarketRegionLabel = (value) => {
      const region = marketRegions.value.find((item) => item.value === value)
      return region ? region.label : '未选择'
    }

    // 获取当前激活的联系人信息
    const activeContactInfo = computed(() => {
      return customerServiceContacts.value.find((c) => c.id === activeContact.value) || {}
    })

    // 发送消息
    const sendMessage = () => {
      if (!messageInput.value.trim()) return

      // 这里实际应用中会发送消息到服务器
      // 现在只是模拟
      messageInput.value = ''

      // 滚动到底部
      nextTick(() => {
        if (chatContainer.value) {
          chatContainer.value.scrollTop = chatContainer.value.scrollHeight
        }
      })
    }
    // 任务等级选项
    const taskLevelOptions = ref([
      { label: '普通', value: 'normal' },
      { label: '紧急', value: 'urgent' },
      { label: '特急', value: 'critical' },
    ])
    // 禁止选择过去日期的方法
    const disablePreviousDates = (ts) => {
      const today = new Date().setHours(0, 0, 0, 0)
      return ts < today
    }

    // 客服联系人数据
    const customerServiceContacts = ref([
      {
        id: 'cs1',
        name: '欧洲市场-小李',
        avatar: 'https://avatars.githubusercontent.com/u/18677354?v=4',
        lastMessage: '我可以帮您查询配件信息',
        time: '9:47',
        unread: 0,
      },
      {
        id: 'cs2',
        name: '中国市场-小王',
        avatar: 'https://avatars.githubusercontent.com/u/18677354?v=4',
        lastMessage: '有什么技术问题可以问我',
        time: '昨天',
        unread: 2,
      },
      // {
      //   id: 'cs3',
      //   name: '订单客服-小张',
      //   avatar: 'https://avatars.githubusercontent.com/u/18677354?v=4',
      //   lastMessage: '我可以帮您处理订单问题',
      //   time: '2025/8/30',
      //   unread: 0,
      // },
    ])

    // const carModels = [
    //   { label: '丰田', value: 'toyota' },
    //   { label: '本田', value: 'honda' },
    //   { label: '大众', value: 'volkswagen' },
    //   { label: '宝马', value: 'bmw' },
    //   { label: '奔驰', value: 'mercedes' },
    // ]

    //const marketRegions = ref([])

    const tagTypeMap = {
      不可供: 'error',
      停供: 'error',
      促销: 'warning',
      可供: 'success',
    }

    const handleSearch = async () => {
      products.value = []
      totalItems.value = 0
      currentPage.value = 1

      loading.value = true

      try {
        searchNumber.value = inputNumber.value
        fetchInquiryResults(inputNumber.value, currentPage.value)
        viewMode.value = 'card'
      } finally {
        loading.value = false
      }
    }

    const subtotal = computed(() => {
      return quoteItems.value
        .reduce((sum, item) => sum + parseFloat(item.price) * item.quantity, 0)
        .toFixed(2)
    })

    const total = computed(() => (parseFloat(subtotal.value) + 20).toFixed(2))

    // 批量询价相关
    const batchModalVisible = ref(false)
    const batchMarketRegion = ref(null)
    const batchItems = ref([{ partCode: '', oem: '', competitorCode: '', brand: '' }]) //, carModel: ''
    const batchCustomerName = ref('')
    const batchCustomerPhone = ref('')
    const batchNotes = ref('')
    // 计算总页数
    const totalPages = computed(() => {
      return Math.ceil(totalItems.value / pageSize.value)
    })

    // 监听页码变化
    watch(currentPage, (newPage) => {
      if (searchNumber.value) {
        fetchInquiryResults(searchNumber.value, newPage)
      }
    })

    // 方法
    const addToQuote = (product) => {
      const existing = quoteItems.value.find((q) => q.id === product.id)
      if (existing) {
        existing.quantity += 1
      } else {
        quoteItems.value.push({ ...product, quantity: 1 })
      }
    }

    const removeItem = (index) => {
      quoteItems.value.splice(index, 1)
    }

    async function fetchInquiryResults(number, page = 1) {
      if (!number) {
        products.value = []
        totalItems.value = 0
        return
      }

      try {
        const response = await api.getInquiry({
          type: currentRole.value,
          numbers: [number],
          part: selectPart.value,
          page: page, // 传递当前页码
          pageSize: pageSize.value, // 传递每页数量
        })

        products.value = response.data.list.map((item) => ({
          id: item._id,
          name: item.Reach_Number,
          price: item.prices && item.prices.length ? item.prices[0] : '暂无价格',
          description: item.Standard_Name + '/参考号：' + item.Brand_Number,
          image:
            item.images && item.images.length
              ? item.images[0]
              : 'http://*************:85/img/product/PIC.jpg',
          tag: item.Available,
        }))
        totalItems.value = response.data.total

        // 新增：当没有搜索结果且是客户角色时，显示询价对话框
        if (currentRole.value === 'customer' && response.data.total === 0) {
          inquiryRequestNumber.value = inputNumber.value
          showNoResultModal.value = true
        }
      } catch (error) {
        console.error('搜索出错:', error)
        // 新增：处理错误时也显示询价对话框
        if (currentRole.value === 'customer') {
          inquiryRequestNumber.value = inputNumber.value
          showNoResultModal.value = true
        }
      } finally {
        loading.value = false
      }
    }

    // 提交询价请求
    const submitInquiryRequest = () => {
      // 这里可以添加API调用将询价请求发送到服务器
      console.log('提交询价请求:', {
        partNumber: inquiryRequestNumber.value,
        carModel: inquiryCarModel.value,
        marketRegion: inquiryMarketRegion.value,
        notes: inquiryNotes.value,
      })

      // 关闭当前对话框并显示成功提示
      showNoResultModal.value = false
      showRequestSubmitted.value = true
    }

    // 关闭成功提示
    const closeRequestSuccess = () => {
      showRequestSubmitted.value = false
      // 清空询价表单
      inquiryRequestNumber.value = ''
      inquiryCarModel.value = null
      inquiryMarketRegion.value = null
      inquiryNotes.value = ''
    }

    const submitQuote = () => {
      showSuccessModal.value = true
    }

    const openBatchModal = () => {
      batchModalVisible.value = true
    }

    const addBatchItem = () => {
      batchItems.value.push({ partCode: '', oem: '', competitorCode: '', brand: '', carModel: '' })
    }

    const removeBatchItem = (index) => {
      if (batchItems.value.length > 1) {
        batchItems.value.splice(index, 1)
      }
    }

    const submitBatchInquiry = () => {
      //showSuccessModal.value = true
      viewMode.value = 'list'
      const rawBatchItems = toRaw(batchItems.value)
      const jsonData = rawBatchItems.map((item, index) => ({
        serialNumber: index + 1,
        partCode: item.partCode,
        oem: item.oem,
        competitorCode: item.competitorCode,
        brand: item.brand,
      }))
      BatchInquiry(JSON.stringify(jsonData, null, 2))
      batchModalVisible.value = false
    }

    async function BatchInquiry(json) {
      const response = await api.get_batchinquiry({
        json: json,
      })
      // 假设返回数据结构为 { data: [...] }
      batchProducts.value = response.data.map((item) => ({
        id: item.序号, // 唯一ID
        name: item.配件名称, // 配件名称
        code: item.配件编码, // 配件编码
        oem: item.OEM, // OEM编码
        competitorBrand: item.竞品品牌, // 竞品品牌
        competitorCode: item.竞品编码, // 竞品编码
        compatibility: item.适配结果, // 适配结果
        price: item.价格, // 价格
      }))
    }

    onMounted(async () => {
      const role = await api.getUserInfo()
      if (role.data.username === 'admin') {
        isadmin.value = true
      } else if (role.data.username === 'test') {
        iscustomer.value = true
      } else if (role.data.username === 'test1') {
        issales.value = true
      } else if (role.data.username === 'test2') {
        issupport.value = true
      }

      const response = await api.getProductLine()
      // 产品线
      partCategories.value = response.data.map((item) => ({
        label: item.name_cn,
        value: item.name_cn,
      }))
      const market = await api.getMarket()
      // 市场分区
      marketRegions.value = market.data.map((item) => ({
        label: item,
        value: item,
      }))
    })

    return {
      isadmin,
      iscustomer,
      issales,
      issupport,
      theme,
      currentRole,
      showSalesFeatures,
      partCategories,
      //carModels,
      marketRegions,
      tagTypeMap,
      products,
      quoteItems,
      quoteNotes,
      selectedMarketRegion,
      currentPage,
      sortOption,
      showSuccessModal,
      handleSearch,
      subtotal,
      total,
      batchModalVisible,
      batchMarketRegion,
      batchItems,
      batchCustomerName,
      batchCustomerPhone,
      batchNotes,
      batchDeadline,
      batchTaskLevel,
      taskLevelOptions,
      disablePreviousDates,
      addToQuote,
      removeItem,
      submitQuote,
      openBatchModal,
      addBatchItem,
      removeBatchItem,
      submitBatchInquiry,
      inputNumber,
      totalItems,
      pageSize,
      totalPages,
      loading,
      showNoResultModal,
      showRequestSubmitted,
      inquiryRequestNumber,
      inquiryCarModel,
      inquiryMarketRegion,
      inquiryNotes,
      submitInquiryRequest,
      closeRequestSuccess,
      //getCarModelLabel,
      getMarketRegionLabel,
      customerServiceContacts,
      activeContact,
      activeContactInfo,
      messageInput,
      chatContainer,
      sendMessage,
      selectMarket,
      selectPart,
      handleFileUpload,
      resetBatchItems,
      viewMode,
      batchProducts,
    }
  },
})
</script>

<style scoped>
/* 优化过渡效果 */
header,
.n-card {
  transition: all 0.3s ease;
}

.scrollable-container {
  height: 750px; /* 固定高度 */
  overflow-y: auto; /* 启用垂直滚动条 */
}
.n-card .n-card-cover img {
  /* display: block; */
  width: 100%;
  height: 100%;
}

body h2,
body h3,
body p,
body span {
  margin-bottom: 10px;
}

. {
  font-size: 0.875rem;
  line-height: 3rem;
}

.max-h-64 {
  max-height: 50rem;
}

/* 优化卡片悬停效果 */
.n-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 优化滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>
