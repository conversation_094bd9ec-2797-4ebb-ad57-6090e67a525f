<template>
  <div ref="tabSelector" class="tab-selector">
    <div
      v-for="(item, index) in options"
      :key="index"
      ref="tabs"
      class="tab"
      :class="{ active: selected === item }"
      @click="selectTab(item)"
    >
      {{ item }}
    </div>
    <div
      class="underline"
      :style="{ left: underlinePosition + 'px', width: underlineWidth + 'px' }"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
const props = defineProps({
  options: {
    type: Array,
    required: true, // 确保父组件传递了这个值
  },
})

const selected = ref(props.options[0])
const underlinePosition = ref(0)
const underlineWidth = ref(0)
const tabs = ref([])

const selectTab = (item) => {
  selected.value = item
  handle_selected(item)
  updateUnderline()
}

const updateUnderline = () => {
  const index = props.options.indexOf(selected.value)
  const tab = tabs.value[index]
  if (tab) {
    underlinePosition.value = tab.offsetLeft + tab.offsetWidth * 0.225 // 向右偏移10%
    underlineWidth.value = tab.offsetWidth * 0.5 // 设置为选项宽度的80%
  }
}

onMounted(() => {
  updateUnderline() // 初始化下划线位置
})

const emits = defineEmits(['selected_value'])
defineExpose({
  selectTab, // 暴露方法供父组件调用
})
const handle_selected = (item) => {
  emits('selected_value', item) // 第一个参数为自定义事件名  第二个参数为要传递的数据
}
</script>

<style scoped>
.tab-selector {
  display: flex;
  position: relative;
  width: 100%; /* 使选择器占满父容器宽度 */
  max-width: 600px; /* 可选：设置最大宽度 */
  margin: 0 auto; /* 可选：居中显示 */
}

.tab {
  flex: 1;
  text-align: center;
  padding: 10px;
  cursor: pointer;
  transition: color 0.3s;
}

.tab.active {
  color: rgb(0, 195, 255); /* 选中状态的颜色 */
}

.underline {
  position: absolute;
  bottom: 0;
  height: 2px;
  background-color: rgb(0, 195, 255);
  transition: left 0.3s, width 0.3s; /* 动画效果 */
}

.tab-selector-container {
  transform: scale(1); /* 默认大小 */
}

@media (max-width: 768px) {
  .tab-selector-container {
    transform: scale(0.8); /* 768px以下的屏幕缩放到0.8倍 */
  }
}

@media (max-width: 480px) {
  .tab-selector-container {
    transform: scale(0.6); /* 480px以下的屏幕缩放到0.6倍 */
  }
}
</style>
