<script setup>
import { ref, onMounted, computed } from 'vue'
import api from '@/api'
import {
  NButton,
  NDataTable,
  NFormItem,
  NGi,
  NGrid, NInput,
  NPagination,
  NSelect
} from 'naive-ui'
const emit = defineEmits(['update:show', 'select']);

// 父组件传过来的数据
const props = defineProps({
  show: Boolean,
  parentShow: Boolean,
  category: String,
  suffix: String,
  selectCarIdsFromIndex: Array // 来自主页的车型id数据
});

console.log("父组件传过来的数据为", props)
console.log("父组件传过来的category数据为", props.category)
console.log("父组件传过来的suffix数据为", props.suffix)

/*---选项属性---*/
const queryItems = ref({
  oes: null,
  category:props.category,
})

// 监听 props.category 变化
watch(
  () => props.category,
  (newVal) => {
    if (newVal) {
      queryItems.value.category = newVal
    }
  },
  { immediate: true } // 立即执行一次，确保初始值也能设置
)

// 监听 props.suffix 变化
watch(
  () => props.suffix,
  (newVal) => {
    if (newVal) {
      props.suffix = newVal
    }
  },
  { immediate: true } // 立即执行一次，确保初始值也能设置
)

// 监听 props.selectCarIdsFromIndex 变化
watch(
  () => props.selectCarIdsFromIndex,
  (newVal) => {
    if (newVal) {
      props.selectCarIdsFromIndex = newVal
    }
  },
  { immediate: true } // 立即执行一次，确保初始值也能设置
)

// ✅ 关键：监听 queryItems.oes 的变化
watch(
  () => queryItems.value.oes,
  (newVal) => {
    // 当输入框内容被清空时
    if (!newVal || newVal.trim() === '') {
      tableData.value = [] // 清空表格数据
      selectedIds.value = [] // 可选：同时清空选中的行
      alert.value = "" // 清空提示
    }
  }
)

// 👉 监听父组件的关闭parentShow
watch(() => props.parentShow, (newVal, oldVal) => {
  if (oldVal === true && newVal === false) {
    // 父组件从 true → false，即被关闭
    queryItems.value.oes = ""
    tableData.value = []
    console.log('父组件关闭，已清空子组件数据')
  }
}, { immediate: false })

// 获取初始选项
const oeCarIds = ref([]) // 自动绑定的车型reach_car_ids
// OE弹窗选择的数据
const selectedRows = ref([])
// OE弹窗查到的车型Ids
const searchReachCarIds = ref([])
// OE弹窗向父组件发送的车型信息
const reachCarInfosToIndex = ref([])
// 是否为单选？默认是单选
const is_single_selected = ref(true)
// 获取全部主号的列表
const allMainNumList = ref([])
// 主号列表是否有替代关系？默认是没有替代关系
const verifyRes = ref(false)
// 是否全部匹配到产品Reach号
const is_OE_all_match_Product = ref(false)
let allRowsKV = ref({})
// 是否包含系统外的OE
const is_extra_OE = ref(false)
// 是否包含其他品类？
const is_other_category = ref(false)
// 是否需要添加备注
const is_need_remark = ref(false)
// 是否为有尾缀的产品
console.log("产品的尾缀为：", props.suffix)
const is_suffix = computed(() => !!props.suffix)
console.log("产品的尾缀为：", is_suffix)
// 是否存在选择的尾缀
const is_exist_select_suffix = ref(false)
// 是否可提交
const is_confirm = ref(true)
const loading = ref(false)
const alert = ref("")
const showModal = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});
const tableData = ref([])
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
})


const columns = [
  {
    type: 'selection',
  },
  {
    title: '唯一id',
    key: '_id',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: 'OE号',
    key: 'Brand_Number',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: 'Reach产品号',
    key: 'Reach_Number',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '主号',
    key: 'MainNum',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '品类名称',
    key: 'Standard_Name',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: 'OE的Tip',
    key: 'Tip',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
]


const selectedIds = ref([]); // 只能选择一个

const safeSelectedIds = computed(() => {
  console.log("------->选择的selectedIds为", selectedIds)
  console.log("------->zhende", Array.isArray(selectedIds.value))
  return Array.isArray(selectedIds.value) ? selectedIds.value : []
})



function handleUpdateCheckedRowKeys(keys) {
  console.log("--------->选择的keys为：", keys)
  if ((is_single_selected.value && keys.length === 0)) {
    selectedIds.value = []
  }else if(is_single_selected.value && keys.length !== 0) {
    // 始终只保留最后一个选中的（即最新点击的）
    selectedIds.value = [keys[keys.length - 1]]
  }else if(is_single_selected.value === false) {
    selectedIds.value = keys
  }
}


/*----------获取OE数据------------*/
// 获取OE列表
const fetchMainNumData = async () => {
  // 初步处理后的tableData
  const afterDealData = ref([])
  // 初始化is_need_remark
  is_need_remark.value = false
  // 初始化is_confirm
  is_confirm.value = true
  // 初始化是否单选
  is_single_selected.value = true
  // 初始化tableData
  tableData.value = []
  // 初始化选项
  selectedIds.value = []
  // 初始化确认选择
  safeSelectedIds.value = []
  // 根据OEs号、品类查询现在存在的产品(重点获取主号)。
  const params = {
    ...queryItems.value,
    page: pagination.value.page,
    pageSize: pagination.value.pageSize,
    timeout: 30000 // 30秒
  }
  console.log("---------->queryItems为", queryItems.value)
  console.log("---------->category为", queryItems.value.category)
  const res = await api.searchOEForComponentMainNum(params)
  tableData.value = res.data || [];
  pagination.value.itemCount = Number(res.total) || 0;
  console.log("------------->tableData数据为", tableData)

  // 获取全部的主号
  allMainNumList.value = [...new Set(tableData.value.map(item => item.MainNum))]
  console.log("allMainNumList", allMainNumList)


  // 如果主号列表中去除空值后的长度仍然大于1，即，仍然存在多个主号，则进行主号替代关系验证
  console.log("allMainNumList去除空值后的值和长度为：", allMainNumList.value.filter(item => item.trim() !== ""), allMainNumList.value.filter(item => item.trim() !== "").length)
  if (allMainNumList.value.filter(item => item.trim() !== "").length > 1){
    // 获取主号是否有替代关系？有替代关系，则只能单选，没有替代关系就默认多选
    verifyRes.value = await verifyReplacement(allMainNumList.value.filter(item => item.trim() !== ""))
  }
  console.log("verifyRes", verifyRes, verifyRes.value)
  // 如果是替代关系的话，就只允许选择一个主号。或者 如果主号列表中去除空值后的长度为0，即，没有主号，则不需要进行替代关系验证，但是仍然可以保持多选
  if (!verifyRes.value || allMainNumList.value.filter(item => item.trim() !== "").length === 0){
    // 关闭只选择一个主号
    is_single_selected.value = false
  }
  console.log("--------->是否能单选？为is_single_selected：", is_single_selected.value)



  // 获取全部系统内{OE:Product}
  allRowsKV = Object.fromEntries(tableData.value.filter(item => item.Tip !== "系统不存在的OE").map(item => [item.Brand_Number, item.Reach_Number]));
  console.log("----------->获取全部系统内{OE:Product}:allRowsKV", allRowsKV)

  // 是否包含系统外部的OE
  is_extra_OE.value = tableData.value.some(item => item.Tip === "系统不存在的OE")
  console.log("是不是包含系统外OE,is_extra_OE:", is_extra_OE)
  // 如果OE全部是系统OE
  if (!is_extra_OE.value){
    console.log("a")
    is_OE_all_match_Product.value = !Object.values(allRowsKV).some(value => value === "" || value == null);
  }else {
    console.log("b")
    // 如果不全是系统OE，默认false
    is_OE_all_match_Product.value = false
  }
  console.log("是否全部匹配到产品Reach号:is_OE_all_match_Product", is_OE_all_match_Product.value)


  // 是否包含其他品类产品
  is_other_category.value = tableData.value.some(item => item.Standard_Name &&
    item.Standard_Name.trim() !== '' &&
    item.Standard_Name !== props.category)
  console.log("是否包含其他品类？is_other_category:", is_other_category)

  // 如果存在系统外部的OE
  console.log("是否存在系统外部的OE", is_extra_OE)
  if (!is_extra_OE.value){
    afterDealData.value = tableData.value
  }else {
    // 如果存在系统外部的OE,就直接去掉这个OE
    afterDealData.value = tableData.value.filter(item=>item.Tip !== "系统不存在的OE")
  }
  console.log("afterDealData为：", afterDealData)

  // 获取全部ReachNUmber的尾缀（前提是有尾缀，没有尾缀的话就啥也不返回）
  const allSuffixList = [
    ...new Set(
      afterDealData.value
        .map(item => {
          const parts = item.Reach_Number.split('.');
          if (parts.length === 4) {
            return parts.pop(); // 或者 parts.pop()
          }
          // 其他情况返回null就行了
          return null;
        })
        .filter(suffix => suffix !== null) // 排除 null 值
    )
  ];


  // 是否包含选择的尾缀
  console.log("选择的尾缀是：props.suffix", props.suffix)
  console.log("所有的尾缀是：allSuffixList", allSuffixList)
  is_exist_select_suffix.value = allSuffixList.some(item => item === props.suffix)
  console.log("是否存在选择的尾缀：is_exist_select_suffix", is_exist_select_suffix.value)

  // 如果包含其他品类直接报错就行了
  if (is_other_category.value){
    alert.value = "重要提醒：OE串查询出了包含其他品类的产品，请确认OE串是否有错！"
    is_confirm.value = false
    return
  }

  /*
    进行逻辑判断处理，弹提示框，打is_need_remark标记
   */
  await dealFetchData()

}


// 切换页面触发
function handlePageChange(page) {
  pagination.value.page = page
  fetchMainNumData()
}

// 调整页面大小触发
function handlePageSizeChange(pageSize) {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  fetchMainNumData()
}

// 条件查询触发
async function handleSearch() {
  pagination.value.page = 1
  await fetchMainNumData()
}

/*

 */
const confirmSelection = async () => {

  if (is_confirm.value === false){
    window.alert("不能创建！")
    return
  }

  // 硬要创建产品的备注
  let remark = ref("")
  console.log("提交时是否需要备注：", is_need_remark, is_need_remark.value)
  if (is_need_remark.value){
    remark = prompt('提示：若要创建，请填写备注：', '');
    if (!remark || remark.trim() === '') {
      alert('备注不能为空！');
      return;
    }
    console.log('用户填写的备注：', remark);
  }

  await sendCreateProductParam()


  // ✅ 通过 emit 把数据传出去（可选）
  emit('select', {
    selectedRows: selectedRows.value, // 完整数据
    reachCarInfosToIndex: reachCarInfosToIndex, // 将全部的车型数据发送给主页
    oeCarIds: oeCarIds.value, // 这是自动绑定的车型数据的ids
    remark: remark
  });

  // ✅ 关闭弹窗
  showModal.value = false;
};

/*
    进行逻辑判断处理，弹提示框，打is_need_remark标记
 */
async function dealFetchData(){
  // 如果全部为系统OE
  console.log("如果全部为系统OE", is_extra_OE)
  if (!is_extra_OE.value){
    console.log("1")
    // 如果全部匹配到产品Reach号
    if (is_OE_all_match_Product.value){
      console.log("2")
      // 匹配到的产品Reach主号唯一
      if((allMainNumList.value.filter(item => item.trim() !== "").length === 1) || (allMainNumList.value.filter(item => item.trim() !== "").length === 0)){
        console.log("3")
        // 有尾缀的产品
        console.log("有没有尾缀", is_suffix)
        if (is_suffix){
          console.log("4")
          // 获得的Reach号中存在选择的产品尾缀
          if (is_exist_select_suffix.value){
            console.log("5")
            alert.value = "提示：此号已有！"
            is_confirm.value = false
          }else {
            console.log("6")
            alert.value = "提示：此OE串中的OE全部匹配到产品，并且主号唯一，但是不存在选择的尾缀，可以创建"
            // 获得的Reach号中不存在选择的产品尾缀
             
          }
        }else {
          console.log("7")
          // 没有尾缀的产品
          alert.value = "提示：存在已有产品！如需继续创建，将进行流水创建！请填写创建备注！"
          is_need_remark.value = true
           
        }
      }else {
        console.log("8")
        // 主号不唯一，进行主号替代关系验证
        // 主号存在替代关系
        if (verifyRes.value){
          console.log("9")
          // 有产品尾缀
          if (is_suffix){
            console.log("10")
            // 获得的Reach号中存在选择的产品尾缀
            if (is_exist_select_suffix.value){
              console.log("11")
              alert.value = "提示：此号已有并且存在该尾缀！本次主号存在替代关系！"
              is_confirm.value = false
            }else {
              console.log("12")
              alert.value = "提示：OE串查询出来的产品主号存在替换关系，请选择其中一个主号进行创建产品！"
              // 获得的Reach号中不存在选择的产品尾缀
               
            }
          }else {
            console.log("13")
            // 没有产品尾缀
            alert.value = "提示：有替代关系，选择一个主号进行创建，必填备注！"
            is_need_remark.value = true
             
          }
        }else {
          console.log("15")
          // 主号不存在替代关系
          // 有产品尾缀
          if (is_suffix){
            console.log("16")
            // 获得的Reach号中存在选择的产品尾缀
            if (is_exist_select_suffix.value){
              console.log("17")
              alert.value = "提示：此号已有或产品数据有误，请修正！"
              is_confirm.value = false
            }else {
              console.log("18")
              // 获得的Reach号中不存在选择的产品尾缀
              alert.value = "提示：OE 1VN ，需验证是否存在替代关系，请修正！"
              is_confirm.value = false
            }
          }else {
            console.log("19")
            // 没有产品尾缀
            alert.value = "提示：此号已有并且主号不存在替换关系，请修正！"
            is_confirm.value = false
          }
        }
      }
    }else {
      console.log("20")
      // 如果没有全部匹配到产品Reach号
      // 主号只有空值
      if ([...new Set(allMainNumList.value)].every(item =>
        item === null ||
        item === undefined ||
        item === '')){
        // 产品有尾缀
        if (is_suffix){
          console.log("21")
          alert.value = "提示：可以创建，将进行 主号流水+尾缀 创建"
        }else {
          // 产品没有尾缀
          console.log("22")
          alert.value = "提示：可以创建，将进行 主号流水 创建"
        }
      }else {
        // 主号不只有空值
        // 去掉空值后，主号唯一
        if (allMainNumList.value.filter(mainNum => mainNum != null && mainNum !== '').length === 1){
          console.log("23")
          alert.value = "提示：弹出提示框：请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
          is_confirm.value = false
        }else {
          // 去掉空值后，主号不唯一
          // 主号不唯一，并且主号存在替代关系
          if (verifyRes.value){
            // 有产品尾缀
            if (is_suffix){
              // 获得的Reach号中存在选择的产品尾缀
              if (is_exist_select_suffix.value){
                console.log("24")
                alert.value = "提示：？？？？？弹出提示框：本次主号存在替代关系，请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
              }else {
                // 获得的Reach号中不存在选择的产品尾缀
                console.log("25")
                alert.value = "提示：？？？？？弹出提示框：本次主号存在替代关系，请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
              }
            }else {
              // 没有产品尾缀
              // 获得的Reach号中存在选择的产品尾缀
              if (is_exist_select_suffix.value){
                console.log("26")
                alert.value = "提示：？？？？？弹出提示框：本次主号存在替代关系，请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
              }else {
                // 获得的Reach号中不存在选择的产品尾缀
                console.log("27")
                alert.value = "提示：？？？？？弹出提示框：本次主号存在替代关系，请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
              }
            }
          }else {
            // 主号不存在替代关系
            // 有产品尾缀
            if (is_suffix){
              // 获得的Reach号中存在选择的产品尾缀
              if (is_exist_select_suffix.value){
                console.log("28")
                alert.value = "提示：？？？？弹出提示框：产品 1VN ，需验证是否存在替代关系，请修正！请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
              }else {
                // 获得的Reach号中不存在选择的产品尾缀
                console.log("29")
                alert.value = "提示：？？？？弹出提示框：产品 1VN ，需验证是否存在替代关系，请修正！请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
              }
            }else {
              // 没有产品尾缀
              // 获得的Reach号中存在选择的产品尾缀
              if (is_exist_select_suffix.value){
                console.log("30")
                alert.value = "提示：？？？？弹出提示框：产品 1VN ，需验证是否存在替代关系，请修正！请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
              }else {
                // 获得的Reach号中不存在选择的产品尾缀
                console.log("31")
                alert.value = "提示：？？？？弹出提示框：产品 1VN ，需验证是否存在替代关系，请修正！请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
              }
            }
          }
        }
      }
    }
  }else {
    // 如果不是全部为系统OE，就去掉非系统OE然后进行逻辑处理
    // 如果全部匹配到产品Reach号
    console.log("不是全部系统OE,此时的is_OE_all_match_Product为：", is_OE_all_match_Product)
    if (is_OE_all_match_Product.value) {
      console.log("42")
      // 匹配到的产品Reach主号唯一。这里我们认定，如果OE匹配出来的数据，主号全部为空，则，认为是唯一主号
      if ((allMainNumList.value.filter(item => item.trim() !== "").length === 1) || (allMainNumList.value.filter(item => item.trim() !== "").length === 0)) {
        console.log("allMainNumList.filter(item => item.trim() !== \"\")", allMainNumList.value.filter(item => item.trim() !== ""))
        console.log("43")
        // 有尾缀的产品
        if (is_suffix) {
          console.log("44")
          // 获得的Reach号中存在选择的产品尾缀
          console.log("是否存在选择的尾缀，is_exist_select_suffix", is_exist_select_suffix)
          if (is_exist_select_suffix.value) {
            console.log("45")
            alert.value = "提示：存在系统外部OE的OE串匹配到了单一主号并且尾缀一致的产品号！请验证该外部OE是够可以添加进该产品！修改产品即可"
            is_confirm.value = false
          } else {
            // 获得的Reach号中不存在选择的产品尾缀
            console.log("46")
            alert.value = "提示：OE串中存在的OE匹配到了单一主号，并且没有选择的尾缀，如需继续创建，请填写备注！"
            is_need_remark.value = true

          }
        } else {
          // 没有尾缀的产品
          console.log("47")
          alert.value = "提示：OE串包含系统外的OE，并且是没有尾缀的品类，并且查询出来的主号唯一。请检查OE是否存在替代关系，如存在修改产品即可。如硬要创建，请填写备注！"
          is_need_remark.value = true
          is_confirm.value = false
        }
      } else {
        console.log("48")
        // 主号不唯一，进行主号替代关系验证
        // 主号存在替代关系
        if (verifyRes.value) {
          console.log("9")
          // 有产品尾缀
          if (is_suffix) {
            console.log("50")
            // 获得的Reach号中存在选择的产品尾缀
            if (is_exist_select_suffix.value) {
              console.log("51")
              alert.value = "提示：此号已有！本次主号存在替代关系！请检查OE串是否存在替换关系。可以修改产品添加系统外的OE！"
              is_confirm.value = false
            } else {
              console.log("52")
              // 获得的Reach号中不存在选择的产品尾缀
              alert.value = "提示：OE串查询出来的产品主号存在替换关系，请选择其中一个主号进行创建产品！并且填写创建备注"
              is_need_remark.value = true

            }
          } else {
            console.log("53")
            // 没有产品尾缀
            alert.value = "提示：本次主号存在替代关系！请检查OE串是否为替代关系，可修改产品以增加新OE！如需继续创建，请填写备注："
            is_need_remark.value = true

          }
        } else {
          console.log("55")
          // 主号不存在替代关系
          // 有产品尾缀
          if (is_suffix) {
            console.log("56")
            // 获得的Reach号中存在选择的产品尾缀
            if (is_exist_select_suffix.value) {
              console.log("57")
              alert.value = "提示：此号已有或产品数据有误，请修正！"
              is_confirm.value = false
            } else {
              console.log("58")
              // 获得的Reach号中不存在选择的产品尾缀
              alert.value = "提示：OE 1VN ，需验证是否存在替代关系，请修正！"
              is_confirm.value = false
            }
          } else {
            console.log("59")
            // 没有产品尾缀
            alert.value = "提示：此号已有或产品数据有误，请修正！"
            is_confirm.value = false
          }
        }
      }
    } else {
      console.log("60")
      // 如果没有全部匹配到产品Reach号
      // 主号去掉空值后为空
      if (allMainNumList.value.filter(mainNum => mainNum != null && mainNum !== '').length === 0){
        if (is_suffix){
          // 尾缀不为空
          alert.value = "获取的主号为空，可以主号流水+加尾缀创建！"
          is_confirm.value = true
          console.log("61")
        }else {
          // 尾缀为空
          alert.value = "获取的主号为空，可以主号流水创建"
          is_confirm.value = true
          console.log("62")
        }
      }else {
        // 主号去掉空值后不为空
        // 去掉空值后，主号唯一
        console.log("allMainNumList为", allMainNumList)
        if (allMainNumList.value.filter(mainNum => mainNum != null && mainNum !== '').length === 1) {
          console.log("63")
          alert.value = "提示：弹出提示框：请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
          is_confirm.value = false
        } else {
          console.log("64")
          // 去掉空值后，主号不唯一，进行主号替代关系验证
          // 主号存在替代关系
          if (verifyRes.value) {
            // 有产品尾缀
            if (is_suffix) {
              // 获得的Reach号中存在选择的产品尾缀
              if (is_exist_select_suffix.value) {
                alert.value = "提示：？？？？？弹出提示框：本次主号存在替代关系，请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
                console.log("65")
              } else {
                // 获得的Reach号中不存在选择的产品尾缀
                alert.value = "提示：？？？？？弹出提示框：本次主号存在替代关系，请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
                console.log("66")
              }
            } else {
              // 没有产品尾缀
              // 获得的Reach号中存在选择的产品尾缀
              if (is_exist_select_suffix.value) {
                alert.value = "提示：？？？？？弹出提示框：本次主号存在替代关系，请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
                console.log("67")
              } else {
                // 获得的Reach号中不存在选择的产品尾缀
                alert.value = "提示：？？？？？弹出提示框：本次主号存在替代关系，请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
                console.log("68")
              }
            }
          } else {
            // 主号不存在替代关系
            // 有产品尾缀
            if (is_suffix) {
              // 获得的Reach号中存在选择的产品尾缀
              if (is_exist_select_suffix.value) {
                alert.value = "提示：？？？？弹出提示框：产品 1VN ，需验证是否存在替代关系，请修正！请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
                console.log("69")
              } else {
                // 获得的Reach号中不存在选择的产品尾缀
                alert.value = "提示：？？？？弹出提示框：产品 1VN ，需验证是否存在替代关系，请修正！请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
                console.log("70")
              }
            } else {
              // 没有产品尾缀
              // 获得的Reach号中存在选择的产品尾缀
              if (is_exist_select_suffix.value) {
                alert.value = "提示：？？？？弹出提示框：产品 1VN ，需验证是否存在替代关系，请修正！请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
                console.log("71")
              } else {
                // 获得的Reach号中不存在选择的产品尾缀
                alert.value = "提示：？？？？弹出提示框：产品 1VN ，需验证是否存在替代关系，请修正！请确认此OE串是否存在替代关系，若存在，请修正系统产品OE；若不存在，请重新输入！"
                is_confirm.value = false
                console.log("72")
              }
            }
          }
        }
      }

    }
  }
}




async function sendCreateProductParam(){

  const reachNumInTableData = tableData.value.map(item => item.Reach_Number);// 计算在tableData中的ReachNum号并存在列表中

  // ✅ 1. 获取选中的行数据
  selectedRows.value = tableData.value.filter(row =>
    selectedIds.value.includes(row._id)
  );
  console.log("----------->selectedRows为", selectedRows)
  // 车型id
  // ✅ 2. 获取该主号，下面所有产品的车型（包含了替换号的）
  if (Array.isArray(selectedRows.value) && selectedRows.value.length > 0) {
    const selectMainNum = selectedRows.value[0].Number
    // 从tableData中获取主号对应的产品reach号，避免直接查询数据库查询出不必要的数据
    const relationReachNumList = tableData.value.filter(item => item['MainNum'] !== selectMainNum).map(item => item['Reach_Number'])
    console.log("---------->relationReachNumList为：", relationReachNumList)
    // 根据所有的产品号，查询其全部车型reach_car_id
    const getReachCarIdsResponse = await api.getReachCarIdsByReachNumber({ "reachNumbersList": relationReachNumList })
    console.log("查询reach号与车型ids的数据getReachCarIdsResponse为：",getReachCarIdsResponse.data)
    searchReachCarIds.value = getReachCarIdsResponse.data.map(item => item.reach_car_id)
    // 如果OE存在系统中，并且是有车型ID的，但是没有Reach产品号，那么，也要去查。是利用OE号去part_num中去查询
    // 过滤出符合以上条件的选择的数据，并且将OE号全部取出来
    const queryCarIdByOEsList = selectedRows.value.filter(item => item["Tip"] === "系统存在的OE" && item["MainNum"] === "").map(item => item["Brand_Number"])
    const getReachCarIdsByOEsResponse = await api.getReachCarIdsByOes({ "queryCarIdByOEsList": queryCarIdByOEsList })
    searchReachCarIds.value.push(...getReachCarIdsByOEsResponse.data.map(item => item.reach_car_id))
    console.log("查询OE与车型ids的数据getReachCarIdsByOEsResponse为：",getReachCarIdsByOEsResponse.data)
    // 如果主页有车型数据了，直接将这个reach_car_id去掉就行
    console.log("----------->现在的车型数据为：", props.selectCarIdsFromIndex)
    // 根据所有reach_car_id进行查询车型信息，在此之前需要先去掉来自主页的selectCar中选择的车型数据
    const selectedCarIdsIndex = props.selectCarIdsFromIndex.map(item => item.reach_car_id)
    oeCarIds.value = searchReachCarIds.value.filter(carId =>
      !selectedCarIdsIndex.includes(carId)
    ) // oeCarIds是排除主页手动选择的车型的结果
    // 去后端查询这些carIds的车型信息
    const getCarInfoByReachCarIdsResponse = await api.getCarInfoByReachCarIds({"reachCarIds":oeCarIds.value})
    console.log("-------->getCarInfoByReachCarIdsResponse为：",getCarInfoByReachCarIdsResponse)
    // reachCarInfosToIndex = 主页手动选择的车型数据 + 自动绑定的车型数据。并发给主页
    reachCarInfosToIndex.value = [...props.selectCarIdsFromIndex, ...getCarInfoByReachCarIdsResponse.data]
    console.log("------------>弹窗向主页发送的车型数据reachCarInfosToIndex为：", reachCarInfosToIndex)
  }

  // 主号
  // 尾缀
  // 备注
}

/*
  验证是否存在替代关系
 */
async function verifyReplacement(allMainNumWithOutNullList){
  const res = await api.verifyReplacementInfo({ "mainNums": allMainNumWithOutNullList })
  return res.data
}


onMounted(() => {
  // fetchMainNumData();
});
</script>

<template>
  <!-- 业务页面 -->
  <n-modal v-model:show="showModal" style="width: 900px;">
    <n-card title="选择适配OE" :bordered="false" size="huge">
      <!-- 表格 -->
      <n-grid :cols="20" :x-gap="12" class="mb-4">
        <n-gi :span="12">
          <n-form-item label="OE搜索" :label-width="60" label-placement="left">
            <n-input
              v-model:value="queryItems.oes"
              placeholder="请输入OE号"
              clearable
              :disabled="loading"
            />
          </n-form-item>
        </n-gi>

        <n-gi :span="4">
          <n-button type="primary" @click="handleSearch">搜索</n-button>
<!--          <n-button class="ml-2" @click="customReset">重置</n-button>-->
        </n-gi>
      </n-grid>


      <span v-if='alert !== ""' style="color: red;">{{alert}}</span>

      <br />
      <!-- 数据表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :bordered="true"
        :single-line="true"
        :row-key="row => row._id"
        :checked-row-keys="selectedIds ? selectedIds : []"
        @update:checked-row-keys="handleUpdateCheckedRowKeys"
        class="flex-1"
      />

      <!-- 分页 -->
      <n-pagination
        :item-count="pagination.itemCount"
        :page-sizes="pagination.pageSizes"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
      <template #footer>
        <div style="text-align: right;">
          <n-button
            type="primary"
            @click="confirmSelection"
            :disabled='safeSelectedIds.length === 0'
          >
            确认选择 ({{ safeSelectedIds.length }})
          </n-button>
          <n-button class="ml-2" @click="showModal = false">取消</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped>

</style>