<template>
  <CommonPage :title="`${typeLabel}管理 - ${tableName}`">
    <!-- 标签类型布局 -->
    <n-grid v-if="isTagType" cols="4" x-gap="12">
      <n-gi :span="1">
        <n-card title="标签结构">
          <n-tree
            :data="treeData"
            key-field="id"
            label-field="name"
            children-field="children"
            @update:selected-keys="handleTreeSelect"
          />
        </n-card>
      </n-gi>
      <n-gi :span="3">
        <DynamicTable
          :columns="dynamicColumns"
          :data="tableData"
          :type="type"
          @refresh="loadTableData"
        />
      </n-gi>
    </n-grid>

    <!-- 字段类型布局 -->
    <n-card v-else title="字段管理">
      <DynamicTable
        :columns="dynamicColumns"
        :data="tableData"
        :type="type"
        @refresh="loadTableData"
      />
    </n-card>
  </CommonPage>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { NTree, NGrid, NGi } from 'naive-ui'
import api from '@/api'
import DynamicTable from '@/components/table/DynamicTable.vue'

const route = useRoute()
const type = ref(route.query.type?.toString() || '')
const tableName = ref(route.query.table?.toString() || '')

// 类型判断
const isTagType = computed(() => type.value === '标签')
const typeLabel = computed(() => (isTagType.value ? '标签' : '字段'))

// 树形数据
const treeData = ref([])
const selectedNode = ref(null)

// 动态表格数据
const dynamicColumns = ref([])
const tableData = ref([])

// 加载树形数据
async function loadTreeData() {
  const res = await api.getTagTreeData({ table: tableName.value })
  treeData.value = res.data
}

// 加载表格配置
async function loadTableConfig() {
  const res = await api.getDynamicColumns({
    // page_type: type.value,
    table: tableName.value,
  })
  dynamicColumns.value = res.data
}

// 加载表格数据
async function loadTableData(params = {}) {
  let payload = {}

  if (isTagType.value) {
    payload = {
      table: tableName.value,
      node: selectedNode.value ? Array.from(selectedNode.value)[0] : null,
      ...params,
    }
  } else {
    payload = {
      table: tableName.value,
      node: '',
      ...params,
    }
  }

  const res = await api.getDynamicTableData(payload)
  tableData.value = res.data
}

// 树节点选择处理
function handleTreeSelect(key) {
  selectedNode.value = key
  loadTableData()
}

async function initLoad() {
  try {
    const initPromises = []

    if (isTagType.value) {
      initPromises.push(loadTreeData())
    }

    initPromises.push(loadTableConfig())

    await Promise.all(initPromises)

    if (!isTagType.value) {
      await loadTableData()
    }
  } catch (e) {
    console.error('初始化失败:', e)
  }
}

// 替换原来的Promise.all初始化
initLoad()
</script>
