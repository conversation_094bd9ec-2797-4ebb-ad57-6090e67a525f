<template>
  <n-card>
    <n-space vertical>
      <n-card size="small">
        <n-space vertical>
          <n-space align="center">
            <n-select
              v-model:value="currentFilter.column"
              :options="filterableColumns"
              placeholder="选择列"
              style="width: 180px"
              filterable
            />
            <n-select
              v-model:value="currentFilter.operator"
              :options="operatorOptions"
              placeholder="选择条件"
              style="width: 140px"
            />
            <n-input
              v-model:value="currentFilter.value"
              placeholder="输入值"
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch"
            />
            <n-button type="primary" @click="handleSearch"> 查询 </n-button>
            <n-button secondary @click="resetFilters">重置</n-button>
          </n-space>

          <!-- 已添加的筛选条件 -->
          <n-space v-if="activeFilters.length > 0" class="filter-tags">
            <n-tag
              v-for="(filter, index) in activeFilters"
              :key="index"
              closable
              @close="removeFilter(index)"
            >
              {{ filter.column.label }}
              {{ operatorMap[filter.operator] }}
              "{{ filter.value }}"
            </n-tag>
          </n-space>
        </n-space>
      </n-card>

      <n-space justify="end">
        <n-button type="primary" @click="showFormModal(null)">+ 新增</n-button>
      </n-space>
      <n-data-table
        :columns="processedColumns"
        :data="paginatedData"
        :row-key="(row) => row.id"
        :pagination="pagination"
        remote
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-space>

    <!-- 动态表单弹窗 -->
    <n-modal v-model:show="showForm">
      <n-card :title="formTitle" style="width: 600px">
        <n-form :model="formModel">
          <n-form-item
            v-for="field in formFields"
            :key="field.key"
            :label="field.title"
            :required="field.required"
          >
            <component
              :is="getComponent(field.type)"
              v-model:value="formModel[field.key]"
              :placeholder="`请输入${field.title}`"
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button @click="showForm = false">取消</n-button>
            <n-button type="primary" @click="handleSubmit">提交</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </n-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { NInput, NInputNumber, NDatePicker, NButton, NSwitch } from 'naive-ui'
import api from '@/api'

// 添加分页相关状态
const page = ref(1)
const pageSize = ref(10)
const currentFilter = ref({ column: null, operator: 'contains', value: '' })
const activeFilters = ref([])
const operatorMap = {
  contains: '包含',
  eq: '等于',
  start: '开头为',
  end: '结尾为',
}

const pagination = computed(() => ({
  page: page.value,
  pageSize: pageSize.value,
  itemCount: filteredData.value.length,
  pageCount: Math.ceil(filteredData.value.length / pageSize.value),
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: () => `共 ${filteredData.value.length} 条`,
}))

// 可筛选列配置
const filterableColumns = computed(() =>
  props.columns
    .filter((c) => c.key !== 'actions')
    .map((c) => ({ label: c.title, value: c.key, type: c.type || 'string' }))
)

const operatorOptions = computed(() => [
  { label: '包含', value: 'contains' },
  { label: '等于', value: 'eq' },
  { label: '开头为', value: 'start' },
  { label: '结尾为', value: 'end' },
])

const filteredData = computed(() => {
  if (activeFilters.value.length === 0) return props.data

  return props.data.filter((row) => {
    return activeFilters.value.every((filter) => {
      // 确保列键名正确
      const columnKey = filter.column
      // 处理空值和类型转换
      const cellValue = String(row[columnKey] ?? '').toLowerCase()
      const filterValue = filter.value.trim().toLowerCase()

      switch (filter.operator) {
        case 'contains':
          return cellValue.includes(filterValue)
        case 'eq':
          return cellValue === filterValue
        case 'start':
          return cellValue.startsWith(filterValue)
        case 'end':
          return cellValue.endsWith(filterValue)
        default:
          return true
      }
    })
  })
})
// 分页数据
const paginatedData = computed(() => {
  const start = (page.value - 1) * pageSize.value
  return filteredData.value.slice(start, start + pageSize.value)
})

// 执行查询
function handleSearch() {
  if (!currentFilter.value.column || !currentFilter.value.value?.trim()) {
    return
  }
  activeFilters.value.push({ ...currentFilter.value })
  currentFilter.value = { column: null, operator: 'contains', value: '' }
  page.value = 1
}

// 重置筛选
function resetFilters() {
  activeFilters.value = []
  page.value = 1
}
function removeFilter(index) {
  activeFilters.value.splice(index, 1)
  page.value = 1
}

// 分页变化处理
function handlePageChange(newPage) {
  page.value = newPage
}

// 分页大小变化处理
function handlePageSizeChange(newSize) {
  pageSize.value = newSize
  page.value = 1
}

const props = defineProps({
  columns: Array,
  data: Array,
  type: String,
})
const emit = defineEmits(['refresh'])

// 动态表单处理
const showForm = ref(false)
const formModel = ref({})
const currentEditId = ref(null)

// 处理带操作列的表格
const processedColumns = computed(() => [
  ...props.columns,
  {
    title: '状态',
    key: 'status',
    render(row) {
      return h(NSwitch, {
        value: row.status === '1',
        loading: row._loading,
        disabled: row._loading,
        onUpdateValue: (val) => ToggleStatusUpdate(row, val ? '1' : '0'),
      })
    },
  },
  {
    title: '操作',
    key: 'actions',
    render: (row) =>
      h('div', { class: 'space-x-2' }, [
        h(
          NButton,
          {
            size: 'small',
            onClick: () => showFormModal(row),
          },
          '编辑'
        ),
      ]),
  },
])

const route = useRoute()
const tableName = ref(route.query.table?.toString() || '')

const ToggleStatusUpdate = async (row, newStatus) => {
  try {
    row._loading = true
    await api.updateTagsFieldsStatus({
      table_name: tableName.value,
      table_id: row.id,
      status: newStatus,
    })

    row.status = newStatus
  } finally {
    row._loading = false
  }
}

// 动态表单字段配置
const formFields = computed(() => props.columns.filter((c) => c.editable !== false))

// 动态组件映射
const componentMap = {
  string: NInput,
  number: NInputNumber,
  date: NDatePicker,
}

// 显示表单弹窗
function showFormModal(row) {
  formModel.value = row ? { ...row } : {}
  currentEditId.value = row?.id || null
  showForm.value = true
}

// 提交表单
async function handleSubmit() {
  const payload = {
    type: props.type,
    data: formModel.value,
    id: currentEditId.value,
  }
  await api.saveDynamicData(payload)
  emit('refresh')
  page.value = 1
  showForm.value = false
}

// 获取对应组件
function getComponent(type) {
  return componentMap[type] || NInput
}
</script>

<style scoped>
.filter-tags {
  margin-top: 12px;
  flex-wrap: wrap;
  row-gap: 8px;
}
</style>
