<script setup>
import { onMounted, ref } from 'vue'
import { NSelect, NButton } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import api from '@/api'

defineOptions({ name: '车型管理' })

const $table = ref(null)
const queryItems = ref({})

onMounted(() => {
  $table.value?.handleSearch()
})

function formatTimestamp(timestamp) {
  const date = new Date(timestamp)

  // const pad = (num) => num.toString().padStart(2, '0')

  const year = date.getFullYear()
  // const month = pad(date.getMonth() + 1) // 月份从0开始，所以需要+1
  // const day = pad(date.getDate())
  // const hours = pad(date.getHours())
  // const minutes = pad(date.getMinutes())
  // const seconds = pad(date.getSeconds())

  // return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  return `${year}`
}

const datetimeRange = ref(null)
const handleDateRangeChange = (value) => {
  if (value == null) {
    queryItems.value.start_time = null
    queryItems.value.end_time = null
  } else {
    queryItems.value.start_time = formatTimestamp(value[0])
    queryItems.value.end_time = formatTimestamp(value[1])
  }
}

// 控制弹窗的状态和数据
const showModal = ref(false)
const modalData = ref({})

const Detail = (rowData) => {
  modalData.value = rowData // 将当前行数据存储到 modalData 中
  showModal.value = true // 打开弹窗
}

const vehicle_type_options = ref([])
const brand_options = ref([])
const series_options = ref([])
const year_options = ref([])
const displacement_options = ref([])
const trans_options = ref([])
const fuel_type_options = ref([])

// 获取初始选项
const loading = ref(false)
const fetchInitOptions = async () => {
  loading.value = true
  try {
    const api_options = await api.getOptionList()
    vehicle_type_options.value = api_options.data.vehicle_type || []
    brand_options.value = api_options.data.brand || []
    series_options.value = api_options.data.series || []
    year_options.value = api_options.data.model_year || []
    displacement_options.value = api_options.data.displacement || []
    trans_options.value = api_options.data.trans_type || []
    fuel_type_options.value = api_options.data.fuel_type || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    loading.value = false // 完成加载
  }
}

const handleOptionChange = async (optionKey, selectedValue) => {
  loading.value = true
  try {
    queryItems.value[optionKey] = selectedValue
    // 从后端获取并更新其它选项
    const updatedOptions = await api.getOptionList(queryItems.value)
    // 更新选项
    vehicle_type_options.value = updatedOptions.data.vehicle_type || []
    brand_options.value = updatedOptions.data.brand || []
    series_options.value = updatedOptions.data.series || []
    year_options.value = updatedOptions.data.model_year || []
    displacement_options.value = updatedOptions.data.displacement || []
    trans_options.value = updatedOptions.data.trans_type || []
    fuel_type_options.value = updatedOptions.data.fuel_type || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchInitOptions()
})

const columns = [
  {
    title: '品牌',
    key: 'brand',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '厂家',
    key: 'manufacturer',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型分类',
    key: 'vehicle_type',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车系',
    key: 'series',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型',
    key: 'model',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '年款',
    key: 'model_year',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '排量',
    key: 'displacement',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: 'Vio',
    key: 'vio',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '更多',
    key: 'actions',
    align: 'center',
    width: 'auto',
    render(row) {
      return h(
        NButton,
        {
          size: 'small',
          onClick: () => Detail(row),
        },
        { default: () => 'Detail' }
      )
    },
  },
]
</script>

<template>
  <!-- 业务页面 -->
  <CommonPage>
    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :pagination="pagination"
      :get-data="api.getCarsList"
    >
      <template #queryBar>
        <QueryBarItem label="车型分类" :label-width="60" :get-data="fetchInitOptions">
          <NSelect
            v-model:value="queryItems.vehicle_type"
            filterable
            clearable
            placeholder="车型分类"
            :options="vehicle_type_options"
            :loading="loading"
            @change="handleOptionChange('vehicle_type', $event)"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <!-- <QueryBarItem label="厂家" :label-width="60">
          <NSelect
            v-model:value="queryItems.manufacturer"
            filterable
            placeholder="厂家"
            :options="manufacturer_options"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem> -->
        <QueryBarItem label="品牌" :label-width="60">
          <NSelect
            v-model:value="queryItems.brand"
            filterable
            clearable
            placeholder="品牌"
            :options="brand_options"
            :loading="loading"
            @change="handleOptionChange('brand', $event)"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="车系" :label-width="60">
          <NSelect
            v-model:value="queryItems.series"
            filterable
            clearable
            placeholder="车系"
            :options="series_options"
            :loading="loading"
            @change="handleOptionChange('series', $event)"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <!-- <QueryBarItem label="车型" :label-width="60">
          <NSelect
            v-model:value="queryItems.model"
            filterable
            placeholder="车型"
            :options="model_options"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem> -->
        <QueryBarItem label="年款" :label-width="60">
          <NSelect
            v-model:value="queryItems.model_year"
            filterable
            clearable
            placeholder="年款"
            :options="year_options"
            :loading="loading"
            @change="handleOptionChange('model_year', $event)"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="排量" :label-width="60">
          <NSelect
            v-model:value="queryItems.displacement"
            filterable
            clearable
            placeholder="排量"
            :options="displacement_options"
            :loading="loading"
            @change="handleOptionChange('displacement', $event)"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="变速器类型" :label-width="60">
          <NSelect
            v-model:value="queryItems.trans_type"
            filterable
            clearable
            placeholder="变速器类型"
            :options="trans_options"
            :loading="loading"
            @change="handleOptionChange('trans_type', $event)"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="燃油类型" :label-width="60">
          <NSelect
            v-model:value="queryItems.fuel_type"
            filterable
            clearable
            placeholder="燃油类型"
            :options="fuel_type_options"
            :loading="loading"
            @change="handleOptionChange('fuel_type', $event)"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="起止日期" :label-width="60">
          <NDatePicker
            v-model:value="datetimeRange"
            type="yearrange"
            clearable
            placeholder="请选择起止日期"
            @update:value="handleDateRangeChange"
          />
        </QueryBarItem>
      </template>
    </CrudTable>
    <!-- 弹窗 -->
    <NModal v-model:show="showModal" preset="dialog" :style="{ width: '800px' }" title="车型详情">
      <NCard>
        <!-- 顶部标题 -->
        <div style="text-align: center; margin-bottom: 20px; font-size: 18px; font-weight: bold">
          {{ modalData.brand }} {{ modalData.series }} {{ modalData.model }};
          {{ modalData.model_year }}
        </div>

        <!-- 参数表格 -->
        <NDescriptions bordered column="2" size="small" title="">
          <!-- 左侧参数 -->
          <NDescriptionsItem label="Reach" label-style="font-weight: bold;">
            {{ modalData.reach_car_id || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="上市年份" label-style="font-weight: bold;">
            {{ modalData.year_from || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="车型分类" label-style="font-weight: bold;">
            {{ modalData.vehicle_type || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="停产年份" label-style="font-weight: bold;">
            {{ modalData.year_till || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="厂家" label-style="font-weight: bold;">
            {{ modalData.manufacturer || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="发动机型号" label-style="font-weight: bold;">
            {{ modalData.engine_code || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="品牌" label-style="font-weight: bold;">
            {{ modalData.brand || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="排量" label-style="font-weight: bold;">
            {{ modalData.displacement || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="车系" label-style="font-weight: bold;">
            {{ modalData.series || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="变速器描述" label-style="font-weight: bold;">
            {{ modalData.trans_desc || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="车型" label-style="font-weight: bold;">
            {{ modalData.model || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="排放标准" label-style="font-weight: bold;">
            {{ modalData.emission_standard || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="驱动类型" label-style="font-weight: bold;">
            {{ modalData.driver_type || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="燃油类型" label-style="font-weight: bold;">
            {{ modalData.fuel_type || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="底盘号" label-style="font-weight: bold;">
            {{ modalData.chassis || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="VIO" label-style="font-weight: bold;">
            {{ modalData.vio || '0' }}
          </NDescriptionsItem>
        </NDescriptions>
      </NCard>
    </NModal>
  </CommonPage>
</template>
