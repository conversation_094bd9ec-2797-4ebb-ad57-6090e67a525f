<script setup>
import { onMounted, ref } from 'vue'
import { NInput, NSelect, NButton } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import api from '@/api'

defineOptions({ name: '车型管理' })

const $table = ref(null)
const queryItems = ref({})

onMounted(() => {
  $table.value?.handleSearch()
})

function formatTimestamp(timestamp) {
  const date = new Date(timestamp)

  // const pad = (num) => num.toString().padStart(2, '0')

  const year = date.getFullYear()
  // const month = pad(date.getMonth() + 1) // 月份从0开始，所以需要+1
  // const day = pad(date.getDate())
  // const hours = pad(date.getHours())
  // const minutes = pad(date.getMinutes())
  // const seconds = pad(date.getSeconds())

  // return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  return `${year}`
}

const datetimeRange = ref(null)
const handleDateRangeChange = (value) => {
  if (value == null) {
    queryItems.value.start_time = null
    queryItems.value.end_time = null
  } else {
    queryItems.value.start_time = formatTimestamp(value[0])
    queryItems.value.end_time = formatTimestamp(value[1])
  }
}

const More = (rowData) => {
  console.log(rowData)
}
const columns = [
  {
    title: '品牌',
    key: 'brand',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '厂家',
    key: 'manufacturer',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型分类',
    key: 'vehicle_type',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车系',
    key: 'series',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型',
    key: 'model',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '年款',
    key: 'model_year',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '排量',
    key: 'displacement',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: 'Vio',
    key: 'vio',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '更多',
    key: 'actions',
    align: 'center',
    width: 'auto',
    render(row) {
      return h(
        NButton,
        {
          size: 'small',
          onClick: () => More(row),
        },
        { default: () => 'More' }
      )
    },
  },
]
</script>

<template>
  <!-- 业务页面 -->
  <CommonPage>
    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :pagination="pagination"
      :get-data="api.getCarsList"
    >
      <template #queryBar>
        <QueryBarItem label="车型分类" :label-width="60">
          <NSelect
            v-model:value="queryItems.vehicle_type"
            filterable
            placeholder="车型分类"
            :options="vehicle_type_options"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="厂家" :label-width="60">
          <NSelect
            v-model:value="queryItems.vehicle_type"
            filterable
            placeholder="厂家"
            :options="manufacturer_options"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="品牌" :label-width="60">
          <NSelect
            v-model:value="queryItems.vehicle_type"
            filterable
            placeholder="品牌"
            :options="brand_options"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="车系" :label-width="60">
          <NSelect
            v-model:value="queryItems.vehicle_type"
            filterable
            placeholder="车系"
            :options="series_options"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="车型" :label-width="60">
          <NSelect
            v-model:value="queryItems.vehicle_type"
            filterable
            placeholder="车型"
            :options="model_options"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="年款" :label-width="60">
          <NSelect
            v-model:value="queryItems.vehicle_type"
            filterable
            placeholder="年款"
            :options="year_options"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="排量" :label-width="60">
          <NSelect
            v-model:value="queryItems.vehicle_type"
            filterable
            placeholder="排量"
            :options="displacement_options"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="变速器类型" :label-width="60">
          <NSelect
            v-model:value="queryItems.vehicle_type"
            filterable
            placeholder="变速器类型"
            :options="trans_options"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="燃油类型" :label-width="60">
          <NSelect
            v-model:value="queryItems.vehicle_type"
            filterable
            placeholder="燃油类型"
            :options="fuel_type_options"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="起止日期" :label-width="60">
          <NDatePicker
            v-model:value="datetimeRange"
            type="yearrange"
            clearable
            placeholder="请选择起止日期"
            @update:value="handleDateRangeChange"
          />
        </QueryBarItem>
      </template>
    </CrudTable>
  </CommonPage>
</template>
