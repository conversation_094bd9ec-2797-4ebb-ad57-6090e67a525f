import { request } from '@/utils'

export default {
  login: (data) => request.post('/v1/base/access_token', data, { noNeedToken: true }),
  getUserInfo: () => request.get('/v1/base/userinfo'),
  getUserMenu: () => request.get('/v1/base/usermenu'),
  getUserApi: () => request.get('/v1/base/userapi'),
  // profile
  updatePassword: (data = {}) => request.post('/v1/base/update_password', data),
  // users
  getUserList: (params = {}) => request.get('/v1/user/list', { params }),
  getUserById: (params = {}) => request.get('/v1/user/get', { params }),
  createUser: (data = {}) => request.post('/v1/user/create', data),
  updateUser: (data = {}) => request.post('/v1/user/update', data),
  deleteUser: (params = {}) => request.delete(`/v1/user/delete`, { params }),
  resetPassword: (data = {}) => request.post(`/v1/user/reset_password`, data),
  // role
  getRoleList: (params = {}) => request.get('/v1/role/list', { params }),
  createRole: (data = {}) => request.post('/v1/role/create', data),
  updateRole: (data = {}) => request.post('/v1/role/update', data),
  deleteRole: (params = {}) => request.delete('/v1/role/delete', { params }),
  updateRoleAuthorized: (data = {}) => request.post('/v1/role/authorized', data),
  getRoleAuthorized: (params = {}) => request.get('/v1/role/authorized', { params }),
  // menus
  getMenus: (params = {}) => request.get('/v1/menu/list', { params }),
  createMenu: (data = {}) => request.post('/v1/menu/create', data),
  updateMenu: (data = {}) => request.post('/v1/menu/update', data),
  deleteMenu: (params = {}) => request.delete('/v1/menu/delete', { params }),
  // apis
  getApis: (params = {}) => request.get('/v1/api/list', { params }),
  createApi: (data = {}) => request.post('/v1/api/create', data),
  updateApi: (data = {}) => request.post('/v1/api/update', data),
  deleteApi: (params = {}) => request.delete('/v1/api/delete', { params }),
  refreshApi: (data = {}) => request.post('/v1/api/refresh', data),
  // depts
  getDepts: (params = {}) => request.get('/v1/dept/list', { params }),
  createDept: (data = {}) => request.post('/v1/dept/create', data),
  updateDept: (data = {}) => request.post('/v1/dept/update', data),
  deleteDept: (params = {}) => request.delete('/v1/dept/delete', { params }),
  // auditlog
  getAuditLogList: (params = {}) => request.get('/v1/auditlog/list', { params }),
  //car_manage
  getCarsList: (params = {}) => request.get('/manage/cars/list', { params }),
}
