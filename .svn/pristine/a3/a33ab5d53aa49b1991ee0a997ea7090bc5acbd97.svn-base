/**
 * 初始化加载效果的svg格式logo
 * @param {string} id - 元素id
 */
 function initSvgLogo(id) {
  const svgStr = `<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="150" height="56" viewBox="0 0 150 56" enable-background="new 0 0 150 56" xml:space="preserve">  <image id="image0" width="150" height="56" x="0" y="0"
  href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAA4CAYAAAAfFwF8AAAACXBIWXMAAAsTAAALEwEAmpwYAAAE9GlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgOS4xLWMwMDIgNzkuYjdjNjRjY2Y5LCAyMDI0LzA3LzE2LTEyOjM5OjA0ICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIgeG1sbnM6cGhvdG9zaG9wPSJodHRwOi8vbnMuYWRvYmUuY29tL3Bob3Rvc2hvcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RFdnQ9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZUV2ZW50IyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgMjYuMSAoTWFjaW50b3NoKSIgeG1wOkNyZWF0ZURhdGU9IjIwMjQtMTItMDNUMTY6NDY6NTYtMDU6MDAiIHhtcDpNb2RpZnlEYXRlPSIyMDI0LTEyLTAzVDE2OjUyOjAyLTA1OjAwIiB4bXA6TWV0YWRhdGFEYXRlPSIyMDI0LTEyLTAzVDE2OjUyOjAyLTA1OjAwIiBkYzpmb3JtYXQ9ImltYWdlL3BuZyIgcGhvdG9zaG9wOkNvbG9yTW9kZT0iMyIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDpmYjE0ZGZiMS01N2Q3LTQ0OTQtOWQ2NC1kZGNhNTNjNGUzODAiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6ZmIxNGRmYjEtNTdkNy00NDk0LTlkNjQtZGRjYTUzYzRlMzgwIiB4bXBNTTpPcmlnaW5hbERvY3VtZW50SUQ9InhtcC5kaWQ6ZmIxNGRmYjEtNTdkNy00NDk0LTlkNjQtZGRjYTUzYzRlMzgwIj4gPHhtcE1NOkhpc3Rvcnk+IDxyZGY6U2VxPiA8cmRmOmxpIHN0RXZ0OmFjdGlvbj0iY3JlYXRlZCIgc3RFdnQ6aW5zdGFuY2VJRD0ieG1wLmlpZDpmYjE0ZGZiMS01N2Q3LTQ0OTQtOWQ2NC1kZGNhNTNjNGUzODAiIHN0RXZ0OndoZW49IjIwMjQtMTItMDNUMTY6NDY6NTYtMDU6MDAiIHN0RXZ0OnNvZnR3YXJlQWdlbnQ9IkFkb2JlIFBob3Rvc2hvcCAyNi4xIChNYWNpbnRvc2gpIi8+IDwvcmRmOlNlcT4gPC94bXBNTTpIaXN0b3J5PiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PiQpLfEAAAziSURBVHic7Z17jB1VHcc/+952d8ulj10p2Nux08XS0kopVouWx1ALRhYU0CARKRAeQiLCQEhUhJCgwIRoAkRQohaMpPKIpBBJHKSgSKFGaLuwvd129rY8umXLdrvb7rO7/jFzt3NnZ/b+zn200cw32XTmzPmdc2bmO7/zO7/f79yWjY2NESNGsVF+rAcQ4/8TMbFilAQxsWKUBDGxYpQEMbFilAQxsWKUBDGxYpQEZcd6ADEmQjOsMqABqAOmAFUcUQJjwCDQD/Q4ttl/TAaZA5HE0gzrVuDKAtoeAXqBT4EUsAl4KdeD0AyrAtgIVBbQdxQGHNv8Uq5KmmFVAa/gvlwVrHds8yf5DEwzrCnAN4HvAl8FpglFdwFPAw87trl7kvZXA/cL27zHsc3nQ9p4GPiKQH5ospdnAEuEA5HigGZYj+IOfCCiznzg9CL3m8E7wnoXI3uAQexSFfC00xrgXmB2Hn3OAe4AfqgZ1q2ObT4aUe/LyN/nJxHl3wCSAvnNk9lYi4SDUME04E7gVc2w6o9ivxlsEda7Kc/2EyqVNcOaCjwPPEF+pPKjBnhEM6zbIq6fqtDW1mCBZlgNyEgFsDWUWJphTcP9EkqF5cBDEdeOKbE0wzoFOCvP9hPSipph1QLrgYvy7CsK93v3EIT0ue52bHN/AfIAW6I0lgq788XVmmHNCilfXMI+JRrrhgLaTyjUfQQ4p4C+olAB3Ogv8Ow3XSgf9YyUNN6xJFYFsDqk/JhpLM2w6oCrCmg/IamkGdYq4OoC+smFVYHzBchdSxOmQQ9FIVYpX64fWV+RNz3MK1Ff+x3b/DBHnStQXwn60aAZ1qQv0DPWLYU2W4GfA48Bo0KZzwXOVWaBQonV69hmR9SqUEqsYeA9XH/LSUCtUC6DROD8FErntN0sqPODIvSTwHWxROFryF/0JuArjm0OwjgprxPIVQXOleyjiHIpsbZCtK9IeuPvOrZ5Boz7fr4PPI7c8Rp8ASrqdieun0yKv012UTOsFeRejncCTTnqHM/kxLomh7wfd2VI5eF9oVwwe1NKrMO4iiILmmHNBqYL2wgnlmZYJ+I+HAnG2e3Y5jDwW82wWoALhfJ7AucqX9bFjm1K3QcS5NJWg8CfgFty1EtEXfBsuBbheDqBlwNlFULZrsC59INNObY5FFKu7KoIm3YKVZuOgnzwC5TewAjQptDPpPBWp5flqPYC0CNoLjHJtfNw/U0SvOTYZtCmkvqRdmYONMOajtxHFvWhqnBiM5SGWNKbGAbezrPv9z0NWSxcDVTnqPOksK3EJNcMYRsA/wgpC/NPhSHlOy7IMZpvG2HEKnQgXxTKbvCHdTTDSgAnCmWLNgV6q7hcvqsu4K/AfkGTiUmu5YxT+vBGSJnU9vXbScUw3KX9djq22QXhxJI20uXYZpaN5BnAUo/92jz7hSISC7gAmJujztMKGjIRVug5KU8TtnGIbK2DZlhNQJhDOQx+YqkoignP1UsKkGrKcfks491rZIGwkSxtpRlWNfCAUDYNrAuUFePLygc35q4ingYhWmOdjjxjY0uIfdUNaEL5j3zH0uc6CIxphjU3UD4XuV3YmjkI3ug85L6o8ZerGdbpwIPAmULZ2wPLaMoZWzQqTw8rCrE0w9KAr+eo1ubY5lvesST3KRFRrpKx8W6wwFutdSi0kYFUY9UAO/Jo349xX2GQWCpq8zrNsK7EdcZNVZD7pWObfw4WNg73LV3S/xGL+z+mr6KG3VUJdlUn2F2doLtiir9qj2ObyukpEbie3D43v7YajKx1BImIcpWpfgKx8oFmWHOQ53UVA+OzWCHEqkGuIsENR9wH3BV2cU9Vw8l7qk7GbpjPyr6dtPS0snCgE4CD5dUuyaoSdFdO6f5OsvliYBvQrqdTea0ONcOqIbezcgz4o2LTiYhyldy2dxT7jMLRiPlmMIbPtiuEWFL0A38BHnBs8z9hFTynbAJgpKycVxp0XmnQ0Qe7aOl5j3N721kwsJcFA3vBnfMz2Y0j7cnmnbj+sG24vq02oE1Pp7pzjOsyYGaOOq85tpn2nUtidYlggWe7LhTIZlAsG/JoxXwBOhzb7MucBIlV7IF0AU8B66JI5SGU0O01M3mocSW/mbGc1b3baOl5jxOGD/irVALN3l+2bLL5E44Qzk+8Dj2dGiU/o/1AaK1shEUtdOS2a4djmyqhqslwNImVFYsdJ5Zizo4UM3FDILdohvUkcG0+IYPeihqeSSzm2cSp3N654Y5Vvamzcd0Ek9lHs7y/lYHywe3J5u3tu58r31kzY8u2mln122pnNe6uStT1l2fFbgeAZya9u3AkQspUtNWEWF0BOJpTYZaXwK+xVHJ28sH3cHOpw1JnRQ9gjDIeaDr78Ru3rn+wPdk8Dze+twZ5bBOgpgwWzR/sYv5gF6vZNn5hqKxib3fFlH3p6uMH9lXWfXDBgbZlJJtTejoVuUkhBImQMhXNUZRQlWZYlcj9T8VAJLFKmbmZwc2aYf3MPxcr9r3Lsc0eAD2d2gHc1p5s/ilwOXAz8IVCBlc9drixaaSvsWmkD1xn5oUA7cnmPiDV46z9cEP9vLa22lkNO2pmfmZ31XEVw2UT4sJ1mmFVOrY54itTIVZrsEAzrKuQJSDuc2zzEu+4mYnpM6VEll3oJ5bKzZ+Jq7JrgDOA35HbEAY3Hncq8K9MgfdlSZ2yE4xaPZ06hLsZ4Yn2ZPMKXIJdSnEfaj2w9LjDA0tbelppORKKHh0or/xkb2X9px3V00fbahtrttfMbDxYXj3LgY998p9X6CtMY61Clof/qu9Y5X3eAUxwAeFmc0jCUMMEIgV+YqlkFmzy2UrrNcP6NSDdTxd0UcwndwA4g6ggKQB6OvUG8EZ7svlHuAlx1yOPP+aD8trRkaY5Q/ub5gztZ2XfeFLBND2bWHMV2gyzsaTvxm9AqxDrdcc2O4KFmmEFM1Gj0BYMefltKulAtoUY4CqrmKBsQbGsMOjpVKeeTt2L+0IvJftLPhoYT4rTDGsm8nTnvcEdMl66tlSjv+k7Ligd2UslahTKT3gv5V4jKjk7YVojV1alHx8HzouR1hEKPZ0a0dOpZ/V06hxgUVtt49/HKDuk0kae8C8m5irIdYSULUUeY/yn71j6XDtCbF5Q03gT3ktmwIVqDanKHAE+CJSpTMHS1NwJWKVf1wecPXV0iG/v39z6rf1bpk0ZHf5svu3lgD+N9wQFueCzAVghlP0wE+ryMlWlAeuovQAFcSIfYoVpjflC2faQ9BOpym6L8IFJcQNQdqi8mt9PX7bwD9OXsfzgro+u3bdxX3KoeyHFdbX4NZZKrC7MpDhPKLved3wK8n0HxUjuiyRW3ikrXqKclFjBVJt65FPFXM2w3hHWzeDHjm2+6KX0ZMUFx4A36+bMfrNuzuymkb7+a/a91XZW745kOWPSTQOTwd+Gyuo0q663iUGadeqPaRbDbpU6dQ8Ewl6AusY6yMSc9pOQr+qCN6HyZdWj/iMlnd6/lzFJklxnZf2U+5rOPe3hmSvWPOusHcV1vC5X7MuPGb7jqB8/CcNizbDKHNvM7LL5BTL7qtWxzdd958WwW6XKJpSYqhprq++mM1DZYBqcz0sZcvBH2yVxwd4DFbXrPL/Y2vZk8zLcHwe5HLUsDsieClVSfBYBL2qGtRHXf3S+UO7ewLn0uQ6DL/TgQTOsJPKVbCixyr1GpHZAGLtViLUzcF5KYu1wbPOQZlhLkCUgPuPY5viKUU+nNunp1BpcP9idqCXZ+afCzbj79aS4ALgbOaleJf9s3An+J0V5iCKWYiNhxFKJR3UEzktJrMwNS3c3B3PwAdDTqX16OnU/7gd0ERP3+oVhXGN5S/kNwjGoohu4yj+LeP4nqfunGNu9Ioml8nLDlqbSvW59jm0G005K+gMg3s8xXSGomybHy9fTqVE9nXpBT6fOx43D/YrofYbBBYB0L4AK+oGWEMO5UEWh2kbJNNaMkLIwZKlczbAakXt288EW3C3/dYK6T4bYjpHQ06ntejp1C+40eQMTP7isbAvHNl/G/VGPYmEPcI5jm2F7D4/mPsKo39JS0lh7HdvcK6wbhuM1w/IHNEudhNaKzGiHiGkwF/R06qCeTj2mp1NLcIPE63AduWEui5uAewh8YIoYww34L3Jsc2NEnUK3e1UiD5pHhtgqcTcISJL3/x1R/jbyHxy7jSNb2RuF/eaDEVyf0JCgjzbHNrcX2qGeTr0GvNaebD4BuL492Vyjp1Pjmy8c2zwM3K0Z1lO4hL8EtS3zzwGPC8Zaiey5DoQFnnEjBdKcMDvqQvxz3McQXoB5Km7aciXZ3v8h3I++L7hV7n8BZfH/VxijFIj/Z4oYJUFMrBglQUysGCVBTKwYJUFMrBglQUysGCVBTKwYJcF/ARTUo/+sLfx0AAAAAElFTkSuQmCC" ></image></svg>`
  const appEl = document.querySelector(id)
  const div = document.createElement('div')
  div.innerHTML = svgStr
  if (appEl) {
    appEl.appendChild(div)
  }
}

function addThemeColorCssVars() {
  const key = '__THEME_COLOR__'
  const defaultColor = '#0866dc'
  const themeColor = window.localStorage.getItem(key) || defaultColor
  const cssVars = `--primary-color: ${themeColor}`
  document.documentElement.style.cssText = cssVars
}

addThemeColorCssVars()

initSvgLogo('#loadingLogo')
