html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

html {
  font-size: 4px; // * 1rem = 4px  方便unocss计算：在unocss中 1字体单位 = 0.25rem，相当于 1等份 = 1px
}

body {
  font-size: 16px;
}

#app {
  width: 100%;
  height: 100%;
}

/* transition fade-slide */
.fade-slide-leave-active,
.fade-slide-enter-active {
  transition: all 0.3s;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 自定义滚动条样式 */
.cus-scroll {
  overflow: auto;
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
}
.cus-scroll-x {
  overflow-x: auto;
  &::-webkit-scrollbar {
    width: 0;
    height: 8px;
  }
}
.cus-scroll-y {
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 8px;
    height: 0;
  }
}
.cus-scroll,
.cus-scroll-x,
.cus-scroll-y {
  &::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 4px;
  }
  &:hover {
    &::-webkit-scrollbar-thumb {
      background: #bfbfbf;
    }
    &::-webkit-scrollbar-thumb:hover {
      background: var(--primary-color);
    }
  }
}
