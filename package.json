{"name": "vue-fastapi-admin-web", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue .", "lint:fix": "eslint --fix --ext .js,.vue .", "lint:staged": "lint-staged", "prettier": "npx prettier --write ."}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/x6": "^2.18.1", "@antv/x6-vue-shape": "^2.1.2", "@iconify/json": "^2.2.228", "@iconify/vue": "^4.1.1", "@unocss/eslint-config": "^0.55.0", "@vicons/antd": "^0.13.0", "@vicons/ionicons-v4": "^0.0.4", "@vicons/ionicons4": "^0.13.0", "@vicons/ionicons5": "^0.13.0", "@vicons/tabler": "^0.13.0", "@vueuse/core": "^10.3.0", "@zclzone/eslint-config": "^0.0.4", "axios": "^1.4.0", "chart.js": "^4.5.0", "dayjs": "^1.11.9", "dotenv": "^16.3.1", "echarts": "^5.6.0", "eslint": "^8.46.0", "file-saver": "^2.0.5", "lodash-es": "^4.17.21", "naive-ui": "^2.41.0", "pinia": "^2.1.6", "postcss": "^8.4.21", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.65.1", "typescript": "^5.1.6", "unocss": "^0.55.0", "unplugin-auto-import": "^0.16.6", "unplugin-icons": "^0.16.5", "unplugin-vue-components": "^0.25.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-i18n": "9", "vue-router": "^4.2.4", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify-json/material-symbols": "^1.2.12", "@types/file-saver": "^2.0.7", "@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.6"}, "lint-staged": {"*.{js,vue}": ["eslint --ext .js,.vue ."]}, "eslintConfig": {"extends": ["@zclzone", "@unocss", ".eslint-global-variables.json"]}}